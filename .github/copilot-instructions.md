---
applyTo: '**'
---

# Project Coding Guidelines

This document provides comprehensive coding standards for AI assistants working on this project.

## Project Overview

This project (`@kdt-farm/solana-grpc-client`) is a TypeScript library that implements clients for multiple Solana gRPC services:
- YellowStone Geyser gRPC
- Corvus ARPC
- And more...

The library is designed to provide a type-safe and easy-to-use interface for interacting with these Solana gRPC services.

## Tech Stack

-   NodeJS LTS - Runtime environment
-   TypeScript - Programming language with static typing
-   PNPM - Package manager
-   @kdt310722/tsconfig - TypeScript configuration
-   ESLint - Code linting and style enforcement
-   gRPC - Remote procedure call framework
-   Protobuf - Protocol Buffers for serialization
-   ts-proto - TypeScript code generation from proto files
-   tsup - TypeScript bundler

## Proto Files and gRPC Clients

### Working with Proto Files

- Proto files are managed as git submodules in the `protos/` directory
- Never modify the proto files directly as they are maintained by their respective projects
- To generate TypeScript code from proto files, use the provided scripts:
  - `pnpm proto:generate` - Generate all clients
  - `pnpm proto:generate:yellowstone` - Generate only YellowStone client
  - `pnpm proto:generate:corvus` - Generate only Corvus client

### Client Implementation Guidelines

- Keep client implementations in their respective directories under `src/clients/`
- Do not modify generated code in the `generated/` directories
- Create wrapper classes that provide a more user-friendly API around the generated clients
- Export client implementations from the respective `index.ts` files

## TypeScript Configuration

### Project-Specific Settings

-   Target ES modules (`"type": "module"` in package.json)
-   Use strict TypeScript configuration from `@kdt310722/tsconfig`

### Type Safety Rules

-   Use TypeScript's strict type checking
-   Prefer `unknown` over `any` type
-   Use type-only imports: `import type { Type }` for types only
-   Prefer inline type imports: `import { type Type, value }` when mixing
-   Define clear types for functions and variables
-   Only specify return types for complex types or when not obvious from code
-   Use utility types (`Pick`, `Omit`, `Partial`) for type manipulation
-   Extract nested interfaces into separate interfaces for reusability

## Code Formatting

### Indentation & Spacing

-   Use 4 spaces for indentation, never tabs
-   No enforced maximum line length
-   Remove trailing whitespace
-   Add blank line at end of files
-   Use LF (`\n`) line endings
-   Place spaces inside object braces: `{ like: this }`
-   Add space before opening braces: `function name() {`

### Punctuation & Symbols

-   No semicolons at end of statements
-   Use single quotes (`'`) for strings in JS/TS
-   Use double quotes (`"`) for JSX attributes
-   Use trailing commas in ES5 style:
    -   Always for multiline arrays and objects
    -   Never for imports/exports
    -   Never for function parameters
-   Always use parentheses with arrow functions: `(param) => {}`
-   Use "one true brace style": opening brace on same line
-   Closing bracket on new line
-   Empty arrow function bodies on single line: `() => {}`

### Line Breaking & Padding

-   Add blank lines before and after major code blocks
-   Add blank line before `return` statements
-   Always blank line before and after: `class`, `interface`, `function`, `if`, `for`, `while`, `switch`, `try`
-   No blank lines between `case` statements in `switch`
-   Add blank line between variable assignment and subsequent method calls

## Import Organization

### Import Order (Strict)

1. Node.js built-in modules (with `node:` prefix)
2. External libraries (alphabetical)
3. Side-effect imports (`import 'module'`)
4. Internal modules (by proximity: `../`, `./`)

### Import Rules

-   Remove unused imports automatically
-   Keep import statements at top of file
-   Keep each import on one line (no line breaks in import statements)
-   Keep export statements on one line without line breaks
-   No import type side effects

## Function & Variable Rules

### Function Guidelines

-   Keep return statements clear and explicit
-   Maximum 30 lines per function (recommended)
-   Maximum 3 levels of nesting depth
-   Prefix unused parameters with underscore: `_error`, `_unused`
-   Use descriptive names indicating purpose
-   Prefer arrow functions with direct return for simple transformations
-   Keep entire return expressions on one line when possible
-   Extract complex inline objects to variables for readability
-   For async functions returning single awaited expression, return directly
-   Omit `await` in direct returns if not needed for error handling

### Variable Rules

-   Use camelCase for variables and functions
-   Use PascalCase for classes and components
-   Use UPPERCASE_SNAKE_CASE for global constants
-   Keep function parameters on same line when reasonable

## Naming Conventions

### File & Directory Naming

-   Files: kebab-case for regular files, PascalCase for component files
-   Directories: kebab-case grouped by functionality
-   TypeORM entities: PascalCase with `.entity.ts` suffix
-   Configuration files: descriptive names in camelCase

### Code Naming

-   Variables and functions: camelCase
-   Classes and interfaces: PascalCase
-   Constants: UPPERCASE_SNAKE_CASE
-   Private class fields: prefer `#privateField` syntax

## Code Organization

### File Structure

1. Imports (following grouping rules)
2. Type definitions and interfaces
3. Constants and configuration
4. Implementation (functions, classes)
5. Exports (prefer named exports, alphabetically organized)

### Class Organization

Structure class members in this order:

1. Public properties
2. Protected properties
3. Private properties (prefer `#privateField` syntax)
4. Constructor
5. Static methods
6. Instance methods (public → protected → private)

#### Class Formatting Rules

-   Add blank lines between access modifier groups
-   Group properties by access modifier with spacing
-   Prefer inline access modifiers for simple constructors: `public constructor(private readonly config: Config) {}`
-   Keep constructor declarations on single line when reasonable

## Code Quality Guidelines

### Performance Considerations

-   Use appropriate data structures for the use case
-   Avoid unnecessary async/await in direct returns

## Code Reuse Guidelines

### Reuse Principles

-   Follow Open/Closed principle: extend without modifying existing code
-   Prefer composition over inheritance
-   Extract reusable logic into utility functions
-   Maintain backward compatibility when extending functionality

### Avoiding Duplication

-   Follow DRY (Don't Repeat Yourself) principle
-   Apply Rule of Three: if code is copy-pasted 3 times, extract it into reusable function
-   Search existing utilities before implementing new functionality

### Maintainability

-   Keep functions focused on single responsibility
-   Use meaningful variable and function names
-   Maintain consistent code organization across modules
-   Use TypeScript's type system to prevent runtime errors
