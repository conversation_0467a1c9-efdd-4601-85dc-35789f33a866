// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v5.29.3
// source: geyser.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientDuplexStream,
  type ClientOptions,
  type ClientUnaryCall,
  type handleBidiStreamingCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { Timestamp } from "./google/protobuf/timestamp";
import {
  BlockHeight,
  Rewards,
  Transaction,
  TransactionError,
  TransactionStatusMeta,
  UnixTimestamp,
} from "./solana-storage";

export enum CommitmentLevel {
  PROCESSED = 0,
  CONFIRMED = 1,
  FINALIZED = 2,
  UNRECOGNIZED = -1,
}

export function commitmentLevelFromJSON(object: any): CommitmentLevel {
  switch (object) {
    case 0:
    case "PROCESSED":
      return CommitmentLevel.PROCESSED;
    case 1:
    case "CONFIRMED":
      return CommitmentLevel.CONFIRMED;
    case 2:
    case "FINALIZED":
      return CommitmentLevel.FINALIZED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommitmentLevel.UNRECOGNIZED;
  }
}

export function commitmentLevelToJSON(object: CommitmentLevel): string {
  switch (object) {
    case CommitmentLevel.PROCESSED:
      return "PROCESSED";
    case CommitmentLevel.CONFIRMED:
      return "CONFIRMED";
    case CommitmentLevel.FINALIZED:
      return "FINALIZED";
    case CommitmentLevel.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum SlotStatus {
  SLOT_PROCESSED = 0,
  SLOT_CONFIRMED = 1,
  SLOT_FINALIZED = 2,
  SLOT_FIRST_SHRED_RECEIVED = 3,
  SLOT_COMPLETED = 4,
  SLOT_CREATED_BANK = 5,
  SLOT_DEAD = 6,
  UNRECOGNIZED = -1,
}

export function slotStatusFromJSON(object: any): SlotStatus {
  switch (object) {
    case 0:
    case "SLOT_PROCESSED":
      return SlotStatus.SLOT_PROCESSED;
    case 1:
    case "SLOT_CONFIRMED":
      return SlotStatus.SLOT_CONFIRMED;
    case 2:
    case "SLOT_FINALIZED":
      return SlotStatus.SLOT_FINALIZED;
    case 3:
    case "SLOT_FIRST_SHRED_RECEIVED":
      return SlotStatus.SLOT_FIRST_SHRED_RECEIVED;
    case 4:
    case "SLOT_COMPLETED":
      return SlotStatus.SLOT_COMPLETED;
    case 5:
    case "SLOT_CREATED_BANK":
      return SlotStatus.SLOT_CREATED_BANK;
    case 6:
    case "SLOT_DEAD":
      return SlotStatus.SLOT_DEAD;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SlotStatus.UNRECOGNIZED;
  }
}

export function slotStatusToJSON(object: SlotStatus): string {
  switch (object) {
    case SlotStatus.SLOT_PROCESSED:
      return "SLOT_PROCESSED";
    case SlotStatus.SLOT_CONFIRMED:
      return "SLOT_CONFIRMED";
    case SlotStatus.SLOT_FINALIZED:
      return "SLOT_FINALIZED";
    case SlotStatus.SLOT_FIRST_SHRED_RECEIVED:
      return "SLOT_FIRST_SHRED_RECEIVED";
    case SlotStatus.SLOT_COMPLETED:
      return "SLOT_COMPLETED";
    case SlotStatus.SLOT_CREATED_BANK:
      return "SLOT_CREATED_BANK";
    case SlotStatus.SLOT_DEAD:
      return "SLOT_DEAD";
    case SlotStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface SubscribeRequest {
  accounts: { [key: string]: SubscribeRequestFilterAccounts };
  slots: { [key: string]: SubscribeRequestFilterSlots };
  transactions: { [key: string]: SubscribeRequestFilterTransactions };
  transactionsStatus: { [key: string]: SubscribeRequestFilterTransactions };
  blocks: { [key: string]: SubscribeRequestFilterBlocks };
  blocksMeta: { [key: string]: SubscribeRequestFilterBlocksMeta };
  entry: { [key: string]: SubscribeRequestFilterEntry };
  commitment?: CommitmentLevel | undefined;
  accountsDataSlice: SubscribeRequestAccountsDataSlice[];
  ping?: SubscribeRequestPing | undefined;
  fromSlot?: bigint | undefined;
}

export interface SubscribeRequest_AccountsEntry {
  key: string;
  value: SubscribeRequestFilterAccounts | undefined;
}

export interface SubscribeRequest_SlotsEntry {
  key: string;
  value: SubscribeRequestFilterSlots | undefined;
}

export interface SubscribeRequest_TransactionsEntry {
  key: string;
  value: SubscribeRequestFilterTransactions | undefined;
}

export interface SubscribeRequest_TransactionsStatusEntry {
  key: string;
  value: SubscribeRequestFilterTransactions | undefined;
}

export interface SubscribeRequest_BlocksEntry {
  key: string;
  value: SubscribeRequestFilterBlocks | undefined;
}

export interface SubscribeRequest_BlocksMetaEntry {
  key: string;
  value: SubscribeRequestFilterBlocksMeta | undefined;
}

export interface SubscribeRequest_EntryEntry {
  key: string;
  value: SubscribeRequestFilterEntry | undefined;
}

export interface SubscribeRequestFilterAccounts {
  account: string[];
  owner: string[];
  filters: SubscribeRequestFilterAccountsFilter[];
  nonemptyTxnSignature?: boolean | undefined;
}

export interface SubscribeRequestFilterAccountsFilter {
  memcmp?: SubscribeRequestFilterAccountsFilterMemcmp | undefined;
  datasize?: bigint | undefined;
  tokenAccountState?: boolean | undefined;
  lamports?: SubscribeRequestFilterAccountsFilterLamports | undefined;
}

export interface SubscribeRequestFilterAccountsFilterMemcmp {
  offset: bigint;
  bytes?: Buffer | undefined;
  base58?: string | undefined;
  base64?: string | undefined;
}

export interface SubscribeRequestFilterAccountsFilterLamports {
  eq?: bigint | undefined;
  ne?: bigint | undefined;
  lt?: bigint | undefined;
  gt?: bigint | undefined;
}

export interface SubscribeRequestFilterSlots {
  filterByCommitment?: boolean | undefined;
  interslotUpdates?: boolean | undefined;
}

export interface SubscribeRequestFilterTransactions {
  vote?: boolean | undefined;
  failed?: boolean | undefined;
  signature?: string | undefined;
  accountInclude: string[];
  accountExclude: string[];
  accountRequired: string[];
}

export interface SubscribeRequestFilterBlocks {
  accountInclude: string[];
  includeTransactions?: boolean | undefined;
  includeAccounts?: boolean | undefined;
  includeEntries?: boolean | undefined;
}

export interface SubscribeRequestFilterBlocksMeta {
}

export interface SubscribeRequestFilterEntry {
}

export interface SubscribeRequestAccountsDataSlice {
  offset: bigint;
  length: bigint;
}

export interface SubscribeRequestPing {
  id: number;
}

export interface SubscribeUpdate {
  filters: string[];
  account?: SubscribeUpdateAccount | undefined;
  slot?: SubscribeUpdateSlot | undefined;
  transaction?: SubscribeUpdateTransaction | undefined;
  transactionStatus?: SubscribeUpdateTransactionStatus | undefined;
  block?: SubscribeUpdateBlock | undefined;
  ping?: SubscribeUpdatePing | undefined;
  pong?: SubscribeUpdatePong | undefined;
  blockMeta?: SubscribeUpdateBlockMeta | undefined;
  entry?: SubscribeUpdateEntry | undefined;
  createdAt: Date | undefined;
}

export interface SubscribeUpdateAccount {
  account: SubscribeUpdateAccountInfo | undefined;
  slot: bigint;
  isStartup: boolean;
}

export interface SubscribeUpdateAccountInfo {
  pubkey: Buffer;
  lamports: bigint;
  owner: Buffer;
  executable: boolean;
  rentEpoch: bigint;
  data: Buffer;
  writeVersion: bigint;
  txnSignature?: Buffer | undefined;
}

export interface SubscribeUpdateSlot {
  slot: bigint;
  parent?: bigint | undefined;
  status: SlotStatus;
  deadError?: string | undefined;
}

export interface SubscribeUpdateTransaction {
  transaction: SubscribeUpdateTransactionInfo | undefined;
  slot: bigint;
}

export interface SubscribeUpdateTransactionInfo {
  signature: Buffer;
  isVote: boolean;
  transaction: Transaction | undefined;
  meta: TransactionStatusMeta | undefined;
  index: bigint;
}

export interface SubscribeUpdateTransactionStatus {
  slot: bigint;
  signature: Buffer;
  isVote: boolean;
  index: bigint;
  err: TransactionError | undefined;
}

export interface SubscribeUpdateBlock {
  slot: bigint;
  blockhash: string;
  rewards: Rewards | undefined;
  blockTime: UnixTimestamp | undefined;
  blockHeight: BlockHeight | undefined;
  parentSlot: bigint;
  parentBlockhash: string;
  executedTransactionCount: bigint;
  transactions: SubscribeUpdateTransactionInfo[];
  updatedAccountCount: bigint;
  accounts: SubscribeUpdateAccountInfo[];
  entriesCount: bigint;
  entries: SubscribeUpdateEntry[];
}

export interface SubscribeUpdateBlockMeta {
  slot: bigint;
  blockhash: string;
  rewards: Rewards | undefined;
  blockTime: UnixTimestamp | undefined;
  blockHeight: BlockHeight | undefined;
  parentSlot: bigint;
  parentBlockhash: string;
  executedTransactionCount: bigint;
  entriesCount: bigint;
}

export interface SubscribeUpdateEntry {
  slot: bigint;
  index: bigint;
  numHashes: bigint;
  hash: Buffer;
  executedTransactionCount: bigint;
  /** added in v1.18, for solana 1.17 value is always 0 */
  startingTransactionIndex: bigint;
}

export interface SubscribeUpdatePing {
}

export interface SubscribeUpdatePong {
  id: number;
}

export interface SubscribeReplayInfoRequest {
}

export interface SubscribeReplayInfoResponse {
  firstAvailable?: bigint | undefined;
}

export interface PingRequest {
  count: number;
}

export interface PongResponse {
  count: number;
}

export interface GetLatestBlockhashRequest {
  commitment?: CommitmentLevel | undefined;
}

export interface GetLatestBlockhashResponse {
  slot: bigint;
  blockhash: string;
  lastValidBlockHeight: bigint;
}

export interface GetBlockHeightRequest {
  commitment?: CommitmentLevel | undefined;
}

export interface GetBlockHeightResponse {
  blockHeight: bigint;
}

export interface GetSlotRequest {
  commitment?: CommitmentLevel | undefined;
}

export interface GetSlotResponse {
  slot: bigint;
}

export interface GetVersionRequest {
}

export interface GetVersionResponse {
  version: string;
}

export interface IsBlockhashValidRequest {
  blockhash: string;
  commitment?: CommitmentLevel | undefined;
}

export interface IsBlockhashValidResponse {
  slot: bigint;
  valid: boolean;
}

function createBaseSubscribeRequest(): SubscribeRequest {
  return {
    accounts: {},
    slots: {},
    transactions: {},
    transactionsStatus: {},
    blocks: {},
    blocksMeta: {},
    entry: {},
    commitment: undefined,
    accountsDataSlice: [],
    ping: undefined,
    fromSlot: undefined,
  };
}

export const SubscribeRequest: MessageFns<SubscribeRequest> = {
  encode(message: SubscribeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.accounts).forEach(([key, value]) => {
      SubscribeRequest_AccountsEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    Object.entries(message.slots).forEach(([key, value]) => {
      SubscribeRequest_SlotsEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.transactions).forEach(([key, value]) => {
      SubscribeRequest_TransactionsEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    Object.entries(message.transactionsStatus).forEach(([key, value]) => {
      SubscribeRequest_TransactionsStatusEntry.encode({ key: key as any, value }, writer.uint32(82).fork()).join();
    });
    Object.entries(message.blocks).forEach(([key, value]) => {
      SubscribeRequest_BlocksEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    Object.entries(message.blocksMeta).forEach(([key, value]) => {
      SubscribeRequest_BlocksMetaEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    Object.entries(message.entry).forEach(([key, value]) => {
      SubscribeRequest_EntryEntry.encode({ key: key as any, value }, writer.uint32(66).fork()).join();
    });
    if (message.commitment !== undefined) {
      writer.uint32(48).int32(message.commitment);
    }
    for (const v of message.accountsDataSlice) {
      SubscribeRequestAccountsDataSlice.encode(v!, writer.uint32(58).fork()).join();
    }
    if (message.ping !== undefined) {
      SubscribeRequestPing.encode(message.ping, writer.uint32(74).fork()).join();
    }
    if (message.fromSlot !== undefined) {
      if (BigInt.asUintN(64, message.fromSlot) !== message.fromSlot) {
        throw new globalThis.Error("value provided for field message.fromSlot of type uint64 too large");
      }
      writer.uint32(88).uint64(message.fromSlot);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = SubscribeRequest_AccountsEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.accounts[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = SubscribeRequest_SlotsEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.slots[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = SubscribeRequest_TransactionsEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.transactions[entry3.key] = entry3.value;
          }
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          const entry10 = SubscribeRequest_TransactionsStatusEntry.decode(reader, reader.uint32());
          if (entry10.value !== undefined) {
            message.transactionsStatus[entry10.key] = entry10.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = SubscribeRequest_BlocksEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.blocks[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = SubscribeRequest_BlocksMetaEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.blocksMeta[entry5.key] = entry5.value;
          }
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          const entry8 = SubscribeRequest_EntryEntry.decode(reader, reader.uint32());
          if (entry8.value !== undefined) {
            message.entry[entry8.key] = entry8.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.commitment = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.accountsDataSlice.push(SubscribeRequestAccountsDataSlice.decode(reader, reader.uint32()));
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.ping = SubscribeRequestPing.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.fromSlot = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequest, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<SubscribeRequest | SubscribeRequest[]> | Iterable<SubscribeRequest | SubscribeRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest.decode(p)];
        }
      } else {
        yield* [SubscribeRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequest {
    return {
      accounts: isObject(object.accounts)
        ? Object.entries(object.accounts).reduce<{ [key: string]: SubscribeRequestFilterAccounts }>(
          (acc, [key, value]) => {
            acc[key] = SubscribeRequestFilterAccounts.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
      slots: isObject(object.slots)
        ? Object.entries(object.slots).reduce<{ [key: string]: SubscribeRequestFilterSlots }>((acc, [key, value]) => {
          acc[key] = SubscribeRequestFilterSlots.fromJSON(value);
          return acc;
        }, {})
        : {},
      transactions: isObject(object.transactions)
        ? Object.entries(object.transactions).reduce<{ [key: string]: SubscribeRequestFilterTransactions }>(
          (acc, [key, value]) => {
            acc[key] = SubscribeRequestFilterTransactions.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
      transactionsStatus: isObject(object.transactionsStatus)
        ? Object.entries(object.transactionsStatus).reduce<{ [key: string]: SubscribeRequestFilterTransactions }>(
          (acc, [key, value]) => {
            acc[key] = SubscribeRequestFilterTransactions.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
      blocks: isObject(object.blocks)
        ? Object.entries(object.blocks).reduce<{ [key: string]: SubscribeRequestFilterBlocks }>((acc, [key, value]) => {
          acc[key] = SubscribeRequestFilterBlocks.fromJSON(value);
          return acc;
        }, {})
        : {},
      blocksMeta: isObject(object.blocksMeta)
        ? Object.entries(object.blocksMeta).reduce<{ [key: string]: SubscribeRequestFilterBlocksMeta }>(
          (acc, [key, value]) => {
            acc[key] = SubscribeRequestFilterBlocksMeta.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
      entry: isObject(object.entry)
        ? Object.entries(object.entry).reduce<{ [key: string]: SubscribeRequestFilterEntry }>((acc, [key, value]) => {
          acc[key] = SubscribeRequestFilterEntry.fromJSON(value);
          return acc;
        }, {})
        : {},
      commitment: isSet(object.commitment) ? commitmentLevelFromJSON(object.commitment) : undefined,
      accountsDataSlice: globalThis.Array.isArray(object?.accountsDataSlice)
        ? object.accountsDataSlice.map((e: any) => SubscribeRequestAccountsDataSlice.fromJSON(e))
        : [],
      ping: isSet(object.ping) ? SubscribeRequestPing.fromJSON(object.ping) : undefined,
      fromSlot: isSet(object.fromSlot) ? BigInt(object.fromSlot) : undefined,
    };
  },

  toJSON(message: SubscribeRequest): unknown {
    const obj: any = {};
    if (message.accounts) {
      const entries = Object.entries(message.accounts);
      if (entries.length > 0) {
        obj.accounts = {};
        entries.forEach(([k, v]) => {
          obj.accounts[k] = SubscribeRequestFilterAccounts.toJSON(v);
        });
      }
    }
    if (message.slots) {
      const entries = Object.entries(message.slots);
      if (entries.length > 0) {
        obj.slots = {};
        entries.forEach(([k, v]) => {
          obj.slots[k] = SubscribeRequestFilterSlots.toJSON(v);
        });
      }
    }
    if (message.transactions) {
      const entries = Object.entries(message.transactions);
      if (entries.length > 0) {
        obj.transactions = {};
        entries.forEach(([k, v]) => {
          obj.transactions[k] = SubscribeRequestFilterTransactions.toJSON(v);
        });
      }
    }
    if (message.transactionsStatus) {
      const entries = Object.entries(message.transactionsStatus);
      if (entries.length > 0) {
        obj.transactionsStatus = {};
        entries.forEach(([k, v]) => {
          obj.transactionsStatus[k] = SubscribeRequestFilterTransactions.toJSON(v);
        });
      }
    }
    if (message.blocks) {
      const entries = Object.entries(message.blocks);
      if (entries.length > 0) {
        obj.blocks = {};
        entries.forEach(([k, v]) => {
          obj.blocks[k] = SubscribeRequestFilterBlocks.toJSON(v);
        });
      }
    }
    if (message.blocksMeta) {
      const entries = Object.entries(message.blocksMeta);
      if (entries.length > 0) {
        obj.blocksMeta = {};
        entries.forEach(([k, v]) => {
          obj.blocksMeta[k] = SubscribeRequestFilterBlocksMeta.toJSON(v);
        });
      }
    }
    if (message.entry) {
      const entries = Object.entries(message.entry);
      if (entries.length > 0) {
        obj.entry = {};
        entries.forEach(([k, v]) => {
          obj.entry[k] = SubscribeRequestFilterEntry.toJSON(v);
        });
      }
    }
    if (message.commitment !== undefined) {
      obj.commitment = commitmentLevelToJSON(message.commitment);
    }
    if (message.accountsDataSlice?.length) {
      obj.accountsDataSlice = message.accountsDataSlice.map((e) => SubscribeRequestAccountsDataSlice.toJSON(e));
    }
    if (message.ping !== undefined) {
      obj.ping = SubscribeRequestPing.toJSON(message.ping);
    }
    if (message.fromSlot !== undefined) {
      obj.fromSlot = message.fromSlot.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequest>, I>>(base?: I): SubscribeRequest {
    return SubscribeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequest>, I>>(object: I): SubscribeRequest {
    const message = createBaseSubscribeRequest();
    message.accounts = Object.entries(object.accounts ?? {}).reduce<{ [key: string]: SubscribeRequestFilterAccounts }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = SubscribeRequestFilterAccounts.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.slots = Object.entries(object.slots ?? {}).reduce<{ [key: string]: SubscribeRequestFilterSlots }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = SubscribeRequestFilterSlots.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.transactions = Object.entries(object.transactions ?? {}).reduce<
      { [key: string]: SubscribeRequestFilterTransactions }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = SubscribeRequestFilterTransactions.fromPartial(value);
      }
      return acc;
    }, {});
    message.transactionsStatus = Object.entries(object.transactionsStatus ?? {}).reduce<
      { [key: string]: SubscribeRequestFilterTransactions }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = SubscribeRequestFilterTransactions.fromPartial(value);
      }
      return acc;
    }, {});
    message.blocks = Object.entries(object.blocks ?? {}).reduce<{ [key: string]: SubscribeRequestFilterBlocks }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = SubscribeRequestFilterBlocks.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.blocksMeta = Object.entries(object.blocksMeta ?? {}).reduce<
      { [key: string]: SubscribeRequestFilterBlocksMeta }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = SubscribeRequestFilterBlocksMeta.fromPartial(value);
      }
      return acc;
    }, {});
    message.entry = Object.entries(object.entry ?? {}).reduce<{ [key: string]: SubscribeRequestFilterEntry }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = SubscribeRequestFilterEntry.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.commitment = object.commitment ?? undefined;
    message.accountsDataSlice =
      object.accountsDataSlice?.map((e) => SubscribeRequestAccountsDataSlice.fromPartial(e)) || [];
    message.ping = (object.ping !== undefined && object.ping !== null)
      ? SubscribeRequestPing.fromPartial(object.ping)
      : undefined;
    message.fromSlot = object.fromSlot ?? undefined;
    return message;
  },
};

function createBaseSubscribeRequest_AccountsEntry(): SubscribeRequest_AccountsEntry {
  return { key: "", value: undefined };
}

export const SubscribeRequest_AccountsEntry: MessageFns<SubscribeRequest_AccountsEntry> = {
  encode(message: SubscribeRequest_AccountsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      SubscribeRequestFilterAccounts.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequest_AccountsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequest_AccountsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = SubscribeRequestFilterAccounts.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequest_AccountsEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequest_AccountsEntry | SubscribeRequest_AccountsEntry[]>
      | Iterable<SubscribeRequest_AccountsEntry | SubscribeRequest_AccountsEntry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_AccountsEntry.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequest_AccountsEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequest_AccountsEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequest_AccountsEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_AccountsEntry.decode(p)];
        }
      } else {
        yield* [SubscribeRequest_AccountsEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequest_AccountsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? SubscribeRequestFilterAccounts.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: SubscribeRequest_AccountsEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = SubscribeRequestFilterAccounts.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequest_AccountsEntry>, I>>(base?: I): SubscribeRequest_AccountsEntry {
    return SubscribeRequest_AccountsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequest_AccountsEntry>, I>>(
    object: I,
  ): SubscribeRequest_AccountsEntry {
    const message = createBaseSubscribeRequest_AccountsEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? SubscribeRequestFilterAccounts.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseSubscribeRequest_SlotsEntry(): SubscribeRequest_SlotsEntry {
  return { key: "", value: undefined };
}

export const SubscribeRequest_SlotsEntry: MessageFns<SubscribeRequest_SlotsEntry> = {
  encode(message: SubscribeRequest_SlotsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      SubscribeRequestFilterSlots.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequest_SlotsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequest_SlotsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = SubscribeRequestFilterSlots.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequest_SlotsEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequest_SlotsEntry | SubscribeRequest_SlotsEntry[]>
      | Iterable<SubscribeRequest_SlotsEntry | SubscribeRequest_SlotsEntry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_SlotsEntry.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequest_SlotsEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequest_SlotsEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequest_SlotsEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_SlotsEntry.decode(p)];
        }
      } else {
        yield* [SubscribeRequest_SlotsEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequest_SlotsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? SubscribeRequestFilterSlots.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: SubscribeRequest_SlotsEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = SubscribeRequestFilterSlots.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequest_SlotsEntry>, I>>(base?: I): SubscribeRequest_SlotsEntry {
    return SubscribeRequest_SlotsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequest_SlotsEntry>, I>>(object: I): SubscribeRequest_SlotsEntry {
    const message = createBaseSubscribeRequest_SlotsEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? SubscribeRequestFilterSlots.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseSubscribeRequest_TransactionsEntry(): SubscribeRequest_TransactionsEntry {
  return { key: "", value: undefined };
}

export const SubscribeRequest_TransactionsEntry: MessageFns<SubscribeRequest_TransactionsEntry> = {
  encode(message: SubscribeRequest_TransactionsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      SubscribeRequestFilterTransactions.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequest_TransactionsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequest_TransactionsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = SubscribeRequestFilterTransactions.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequest_TransactionsEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequest_TransactionsEntry | SubscribeRequest_TransactionsEntry[]>
      | Iterable<SubscribeRequest_TransactionsEntry | SubscribeRequest_TransactionsEntry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_TransactionsEntry.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequest_TransactionsEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequest_TransactionsEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequest_TransactionsEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_TransactionsEntry.decode(p)];
        }
      } else {
        yield* [SubscribeRequest_TransactionsEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequest_TransactionsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? SubscribeRequestFilterTransactions.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: SubscribeRequest_TransactionsEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = SubscribeRequestFilterTransactions.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequest_TransactionsEntry>, I>>(
    base?: I,
  ): SubscribeRequest_TransactionsEntry {
    return SubscribeRequest_TransactionsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequest_TransactionsEntry>, I>>(
    object: I,
  ): SubscribeRequest_TransactionsEntry {
    const message = createBaseSubscribeRequest_TransactionsEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? SubscribeRequestFilterTransactions.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseSubscribeRequest_TransactionsStatusEntry(): SubscribeRequest_TransactionsStatusEntry {
  return { key: "", value: undefined };
}

export const SubscribeRequest_TransactionsStatusEntry: MessageFns<SubscribeRequest_TransactionsStatusEntry> = {
  encode(message: SubscribeRequest_TransactionsStatusEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      SubscribeRequestFilterTransactions.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequest_TransactionsStatusEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequest_TransactionsStatusEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = SubscribeRequestFilterTransactions.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequest_TransactionsStatusEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequest_TransactionsStatusEntry | SubscribeRequest_TransactionsStatusEntry[]>
      | Iterable<SubscribeRequest_TransactionsStatusEntry | SubscribeRequest_TransactionsStatusEntry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_TransactionsStatusEntry.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequest_TransactionsStatusEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequest_TransactionsStatusEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequest_TransactionsStatusEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_TransactionsStatusEntry.decode(p)];
        }
      } else {
        yield* [SubscribeRequest_TransactionsStatusEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequest_TransactionsStatusEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? SubscribeRequestFilterTransactions.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: SubscribeRequest_TransactionsStatusEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = SubscribeRequestFilterTransactions.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequest_TransactionsStatusEntry>, I>>(
    base?: I,
  ): SubscribeRequest_TransactionsStatusEntry {
    return SubscribeRequest_TransactionsStatusEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequest_TransactionsStatusEntry>, I>>(
    object: I,
  ): SubscribeRequest_TransactionsStatusEntry {
    const message = createBaseSubscribeRequest_TransactionsStatusEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? SubscribeRequestFilterTransactions.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseSubscribeRequest_BlocksEntry(): SubscribeRequest_BlocksEntry {
  return { key: "", value: undefined };
}

export const SubscribeRequest_BlocksEntry: MessageFns<SubscribeRequest_BlocksEntry> = {
  encode(message: SubscribeRequest_BlocksEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      SubscribeRequestFilterBlocks.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequest_BlocksEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequest_BlocksEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = SubscribeRequestFilterBlocks.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequest_BlocksEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequest_BlocksEntry | SubscribeRequest_BlocksEntry[]>
      | Iterable<SubscribeRequest_BlocksEntry | SubscribeRequest_BlocksEntry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_BlocksEntry.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequest_BlocksEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequest_BlocksEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequest_BlocksEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_BlocksEntry.decode(p)];
        }
      } else {
        yield* [SubscribeRequest_BlocksEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequest_BlocksEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? SubscribeRequestFilterBlocks.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: SubscribeRequest_BlocksEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = SubscribeRequestFilterBlocks.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequest_BlocksEntry>, I>>(base?: I): SubscribeRequest_BlocksEntry {
    return SubscribeRequest_BlocksEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequest_BlocksEntry>, I>>(object: I): SubscribeRequest_BlocksEntry {
    const message = createBaseSubscribeRequest_BlocksEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? SubscribeRequestFilterBlocks.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseSubscribeRequest_BlocksMetaEntry(): SubscribeRequest_BlocksMetaEntry {
  return { key: "", value: undefined };
}

export const SubscribeRequest_BlocksMetaEntry: MessageFns<SubscribeRequest_BlocksMetaEntry> = {
  encode(message: SubscribeRequest_BlocksMetaEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      SubscribeRequestFilterBlocksMeta.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequest_BlocksMetaEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequest_BlocksMetaEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = SubscribeRequestFilterBlocksMeta.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequest_BlocksMetaEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequest_BlocksMetaEntry | SubscribeRequest_BlocksMetaEntry[]>
      | Iterable<SubscribeRequest_BlocksMetaEntry | SubscribeRequest_BlocksMetaEntry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_BlocksMetaEntry.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequest_BlocksMetaEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequest_BlocksMetaEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequest_BlocksMetaEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_BlocksMetaEntry.decode(p)];
        }
      } else {
        yield* [SubscribeRequest_BlocksMetaEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequest_BlocksMetaEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? SubscribeRequestFilterBlocksMeta.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: SubscribeRequest_BlocksMetaEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = SubscribeRequestFilterBlocksMeta.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequest_BlocksMetaEntry>, I>>(
    base?: I,
  ): SubscribeRequest_BlocksMetaEntry {
    return SubscribeRequest_BlocksMetaEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequest_BlocksMetaEntry>, I>>(
    object: I,
  ): SubscribeRequest_BlocksMetaEntry {
    const message = createBaseSubscribeRequest_BlocksMetaEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? SubscribeRequestFilterBlocksMeta.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseSubscribeRequest_EntryEntry(): SubscribeRequest_EntryEntry {
  return { key: "", value: undefined };
}

export const SubscribeRequest_EntryEntry: MessageFns<SubscribeRequest_EntryEntry> = {
  encode(message: SubscribeRequest_EntryEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      SubscribeRequestFilterEntry.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequest_EntryEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequest_EntryEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = SubscribeRequestFilterEntry.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequest_EntryEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequest_EntryEntry | SubscribeRequest_EntryEntry[]>
      | Iterable<SubscribeRequest_EntryEntry | SubscribeRequest_EntryEntry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_EntryEntry.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequest_EntryEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequest_EntryEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequest_EntryEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest_EntryEntry.decode(p)];
        }
      } else {
        yield* [SubscribeRequest_EntryEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequest_EntryEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? SubscribeRequestFilterEntry.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: SubscribeRequest_EntryEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = SubscribeRequestFilterEntry.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequest_EntryEntry>, I>>(base?: I): SubscribeRequest_EntryEntry {
    return SubscribeRequest_EntryEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequest_EntryEntry>, I>>(object: I): SubscribeRequest_EntryEntry {
    const message = createBaseSubscribeRequest_EntryEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? SubscribeRequestFilterEntry.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseSubscribeRequestFilterAccounts(): SubscribeRequestFilterAccounts {
  return { account: [], owner: [], filters: [], nonemptyTxnSignature: undefined };
}

export const SubscribeRequestFilterAccounts: MessageFns<SubscribeRequestFilterAccounts> = {
  encode(message: SubscribeRequestFilterAccounts, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.account) {
      writer.uint32(18).string(v!);
    }
    for (const v of message.owner) {
      writer.uint32(26).string(v!);
    }
    for (const v of message.filters) {
      SubscribeRequestFilterAccountsFilter.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.nonemptyTxnSignature !== undefined) {
      writer.uint32(40).bool(message.nonemptyTxnSignature);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterAccounts {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterAccounts();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.account.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.owner.push(reader.string());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.filters.push(SubscribeRequestFilterAccountsFilter.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.nonemptyTxnSignature = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterAccounts, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterAccounts | SubscribeRequestFilterAccounts[]>
      | Iterable<SubscribeRequestFilterAccounts | SubscribeRequestFilterAccounts[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccounts.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterAccounts.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterAccounts>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterAccounts> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccounts.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterAccounts.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterAccounts {
    return {
      account: globalThis.Array.isArray(object?.account) ? object.account.map((e: any) => globalThis.String(e)) : [],
      owner: globalThis.Array.isArray(object?.owner) ? object.owner.map((e: any) => globalThis.String(e)) : [],
      filters: globalThis.Array.isArray(object?.filters)
        ? object.filters.map((e: any) => SubscribeRequestFilterAccountsFilter.fromJSON(e))
        : [],
      nonemptyTxnSignature: isSet(object.nonemptyTxnSignature)
        ? globalThis.Boolean(object.nonemptyTxnSignature)
        : undefined,
    };
  },

  toJSON(message: SubscribeRequestFilterAccounts): unknown {
    const obj: any = {};
    if (message.account?.length) {
      obj.account = message.account;
    }
    if (message.owner?.length) {
      obj.owner = message.owner;
    }
    if (message.filters?.length) {
      obj.filters = message.filters.map((e) => SubscribeRequestFilterAccountsFilter.toJSON(e));
    }
    if (message.nonemptyTxnSignature !== undefined) {
      obj.nonemptyTxnSignature = message.nonemptyTxnSignature;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterAccounts>, I>>(base?: I): SubscribeRequestFilterAccounts {
    return SubscribeRequestFilterAccounts.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterAccounts>, I>>(
    object: I,
  ): SubscribeRequestFilterAccounts {
    const message = createBaseSubscribeRequestFilterAccounts();
    message.account = object.account?.map((e) => e) || [];
    message.owner = object.owner?.map((e) => e) || [];
    message.filters = object.filters?.map((e) => SubscribeRequestFilterAccountsFilter.fromPartial(e)) || [];
    message.nonemptyTxnSignature = object.nonemptyTxnSignature ?? undefined;
    return message;
  },
};

function createBaseSubscribeRequestFilterAccountsFilter(): SubscribeRequestFilterAccountsFilter {
  return { memcmp: undefined, datasize: undefined, tokenAccountState: undefined, lamports: undefined };
}

export const SubscribeRequestFilterAccountsFilter: MessageFns<SubscribeRequestFilterAccountsFilter> = {
  encode(message: SubscribeRequestFilterAccountsFilter, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.memcmp !== undefined) {
      SubscribeRequestFilterAccountsFilterMemcmp.encode(message.memcmp, writer.uint32(10).fork()).join();
    }
    if (message.datasize !== undefined) {
      if (BigInt.asUintN(64, message.datasize) !== message.datasize) {
        throw new globalThis.Error("value provided for field message.datasize of type uint64 too large");
      }
      writer.uint32(16).uint64(message.datasize);
    }
    if (message.tokenAccountState !== undefined) {
      writer.uint32(24).bool(message.tokenAccountState);
    }
    if (message.lamports !== undefined) {
      SubscribeRequestFilterAccountsFilterLamports.encode(message.lamports, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterAccountsFilter {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterAccountsFilter();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.memcmp = SubscribeRequestFilterAccountsFilterMemcmp.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.datasize = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.tokenAccountState = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.lamports = SubscribeRequestFilterAccountsFilterLamports.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterAccountsFilter, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterAccountsFilter | SubscribeRequestFilterAccountsFilter[]>
      | Iterable<SubscribeRequestFilterAccountsFilter | SubscribeRequestFilterAccountsFilter[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccountsFilter.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterAccountsFilter.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterAccountsFilter>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterAccountsFilter> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccountsFilter.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterAccountsFilter.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterAccountsFilter {
    return {
      memcmp: isSet(object.memcmp) ? SubscribeRequestFilterAccountsFilterMemcmp.fromJSON(object.memcmp) : undefined,
      datasize: isSet(object.datasize) ? BigInt(object.datasize) : undefined,
      tokenAccountState: isSet(object.tokenAccountState) ? globalThis.Boolean(object.tokenAccountState) : undefined,
      lamports: isSet(object.lamports)
        ? SubscribeRequestFilterAccountsFilterLamports.fromJSON(object.lamports)
        : undefined,
    };
  },

  toJSON(message: SubscribeRequestFilterAccountsFilter): unknown {
    const obj: any = {};
    if (message.memcmp !== undefined) {
      obj.memcmp = SubscribeRequestFilterAccountsFilterMemcmp.toJSON(message.memcmp);
    }
    if (message.datasize !== undefined) {
      obj.datasize = message.datasize.toString();
    }
    if (message.tokenAccountState !== undefined) {
      obj.tokenAccountState = message.tokenAccountState;
    }
    if (message.lamports !== undefined) {
      obj.lamports = SubscribeRequestFilterAccountsFilterLamports.toJSON(message.lamports);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterAccountsFilter>, I>>(
    base?: I,
  ): SubscribeRequestFilterAccountsFilter {
    return SubscribeRequestFilterAccountsFilter.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterAccountsFilter>, I>>(
    object: I,
  ): SubscribeRequestFilterAccountsFilter {
    const message = createBaseSubscribeRequestFilterAccountsFilter();
    message.memcmp = (object.memcmp !== undefined && object.memcmp !== null)
      ? SubscribeRequestFilterAccountsFilterMemcmp.fromPartial(object.memcmp)
      : undefined;
    message.datasize = object.datasize ?? undefined;
    message.tokenAccountState = object.tokenAccountState ?? undefined;
    message.lamports = (object.lamports !== undefined && object.lamports !== null)
      ? SubscribeRequestFilterAccountsFilterLamports.fromPartial(object.lamports)
      : undefined;
    return message;
  },
};

function createBaseSubscribeRequestFilterAccountsFilterMemcmp(): SubscribeRequestFilterAccountsFilterMemcmp {
  return { offset: 0n, bytes: undefined, base58: undefined, base64: undefined };
}

export const SubscribeRequestFilterAccountsFilterMemcmp: MessageFns<SubscribeRequestFilterAccountsFilterMemcmp> = {
  encode(message: SubscribeRequestFilterAccountsFilterMemcmp, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.offset !== 0n) {
      if (BigInt.asUintN(64, message.offset) !== message.offset) {
        throw new globalThis.Error("value provided for field message.offset of type uint64 too large");
      }
      writer.uint32(8).uint64(message.offset);
    }
    if (message.bytes !== undefined) {
      writer.uint32(18).bytes(message.bytes);
    }
    if (message.base58 !== undefined) {
      writer.uint32(26).string(message.base58);
    }
    if (message.base64 !== undefined) {
      writer.uint32(34).string(message.base64);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterAccountsFilterMemcmp {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterAccountsFilterMemcmp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.offset = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bytes = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.base58 = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.base64 = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterAccountsFilterMemcmp, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterAccountsFilterMemcmp | SubscribeRequestFilterAccountsFilterMemcmp[]>
      | Iterable<SubscribeRequestFilterAccountsFilterMemcmp | SubscribeRequestFilterAccountsFilterMemcmp[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccountsFilterMemcmp.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterAccountsFilterMemcmp.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterAccountsFilterMemcmp>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterAccountsFilterMemcmp> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccountsFilterMemcmp.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterAccountsFilterMemcmp.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterAccountsFilterMemcmp {
    return {
      offset: isSet(object.offset) ? BigInt(object.offset) : 0n,
      bytes: isSet(object.bytes) ? Buffer.from(bytesFromBase64(object.bytes)) : undefined,
      base58: isSet(object.base58) ? globalThis.String(object.base58) : undefined,
      base64: isSet(object.base64) ? globalThis.String(object.base64) : undefined,
    };
  },

  toJSON(message: SubscribeRequestFilterAccountsFilterMemcmp): unknown {
    const obj: any = {};
    if (message.offset !== 0n) {
      obj.offset = message.offset.toString();
    }
    if (message.bytes !== undefined) {
      obj.bytes = base64FromBytes(message.bytes);
    }
    if (message.base58 !== undefined) {
      obj.base58 = message.base58;
    }
    if (message.base64 !== undefined) {
      obj.base64 = message.base64;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterAccountsFilterMemcmp>, I>>(
    base?: I,
  ): SubscribeRequestFilterAccountsFilterMemcmp {
    return SubscribeRequestFilterAccountsFilterMemcmp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterAccountsFilterMemcmp>, I>>(
    object: I,
  ): SubscribeRequestFilterAccountsFilterMemcmp {
    const message = createBaseSubscribeRequestFilterAccountsFilterMemcmp();
    message.offset = object.offset ?? 0n;
    message.bytes = object.bytes ?? undefined;
    message.base58 = object.base58 ?? undefined;
    message.base64 = object.base64 ?? undefined;
    return message;
  },
};

function createBaseSubscribeRequestFilterAccountsFilterLamports(): SubscribeRequestFilterAccountsFilterLamports {
  return { eq: undefined, ne: undefined, lt: undefined, gt: undefined };
}

export const SubscribeRequestFilterAccountsFilterLamports: MessageFns<SubscribeRequestFilterAccountsFilterLamports> = {
  encode(
    message: SubscribeRequestFilterAccountsFilterLamports,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.eq !== undefined) {
      if (BigInt.asUintN(64, message.eq) !== message.eq) {
        throw new globalThis.Error("value provided for field message.eq of type uint64 too large");
      }
      writer.uint32(8).uint64(message.eq);
    }
    if (message.ne !== undefined) {
      if (BigInt.asUintN(64, message.ne) !== message.ne) {
        throw new globalThis.Error("value provided for field message.ne of type uint64 too large");
      }
      writer.uint32(16).uint64(message.ne);
    }
    if (message.lt !== undefined) {
      if (BigInt.asUintN(64, message.lt) !== message.lt) {
        throw new globalThis.Error("value provided for field message.lt of type uint64 too large");
      }
      writer.uint32(24).uint64(message.lt);
    }
    if (message.gt !== undefined) {
      if (BigInt.asUintN(64, message.gt) !== message.gt) {
        throw new globalThis.Error("value provided for field message.gt of type uint64 too large");
      }
      writer.uint32(32).uint64(message.gt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterAccountsFilterLamports {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterAccountsFilterLamports();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.eq = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.ne = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.lt = reader.uint64() as bigint;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.gt = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterAccountsFilterLamports, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterAccountsFilterLamports | SubscribeRequestFilterAccountsFilterLamports[]>
      | Iterable<SubscribeRequestFilterAccountsFilterLamports | SubscribeRequestFilterAccountsFilterLamports[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccountsFilterLamports.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterAccountsFilterLamports.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterAccountsFilterLamports>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterAccountsFilterLamports> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterAccountsFilterLamports.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterAccountsFilterLamports.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterAccountsFilterLamports {
    return {
      eq: isSet(object.eq) ? BigInt(object.eq) : undefined,
      ne: isSet(object.ne) ? BigInt(object.ne) : undefined,
      lt: isSet(object.lt) ? BigInt(object.lt) : undefined,
      gt: isSet(object.gt) ? BigInt(object.gt) : undefined,
    };
  },

  toJSON(message: SubscribeRequestFilterAccountsFilterLamports): unknown {
    const obj: any = {};
    if (message.eq !== undefined) {
      obj.eq = message.eq.toString();
    }
    if (message.ne !== undefined) {
      obj.ne = message.ne.toString();
    }
    if (message.lt !== undefined) {
      obj.lt = message.lt.toString();
    }
    if (message.gt !== undefined) {
      obj.gt = message.gt.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterAccountsFilterLamports>, I>>(
    base?: I,
  ): SubscribeRequestFilterAccountsFilterLamports {
    return SubscribeRequestFilterAccountsFilterLamports.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterAccountsFilterLamports>, I>>(
    object: I,
  ): SubscribeRequestFilterAccountsFilterLamports {
    const message = createBaseSubscribeRequestFilterAccountsFilterLamports();
    message.eq = object.eq ?? undefined;
    message.ne = object.ne ?? undefined;
    message.lt = object.lt ?? undefined;
    message.gt = object.gt ?? undefined;
    return message;
  },
};

function createBaseSubscribeRequestFilterSlots(): SubscribeRequestFilterSlots {
  return { filterByCommitment: undefined, interslotUpdates: undefined };
}

export const SubscribeRequestFilterSlots: MessageFns<SubscribeRequestFilterSlots> = {
  encode(message: SubscribeRequestFilterSlots, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.filterByCommitment !== undefined) {
      writer.uint32(8).bool(message.filterByCommitment);
    }
    if (message.interslotUpdates !== undefined) {
      writer.uint32(16).bool(message.interslotUpdates);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterSlots {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterSlots();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.filterByCommitment = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.interslotUpdates = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterSlots, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterSlots | SubscribeRequestFilterSlots[]>
      | Iterable<SubscribeRequestFilterSlots | SubscribeRequestFilterSlots[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterSlots.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterSlots.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterSlots>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterSlots> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterSlots.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterSlots.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterSlots {
    return {
      filterByCommitment: isSet(object.filterByCommitment) ? globalThis.Boolean(object.filterByCommitment) : undefined,
      interslotUpdates: isSet(object.interslotUpdates) ? globalThis.Boolean(object.interslotUpdates) : undefined,
    };
  },

  toJSON(message: SubscribeRequestFilterSlots): unknown {
    const obj: any = {};
    if (message.filterByCommitment !== undefined) {
      obj.filterByCommitment = message.filterByCommitment;
    }
    if (message.interslotUpdates !== undefined) {
      obj.interslotUpdates = message.interslotUpdates;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterSlots>, I>>(base?: I): SubscribeRequestFilterSlots {
    return SubscribeRequestFilterSlots.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterSlots>, I>>(object: I): SubscribeRequestFilterSlots {
    const message = createBaseSubscribeRequestFilterSlots();
    message.filterByCommitment = object.filterByCommitment ?? undefined;
    message.interslotUpdates = object.interslotUpdates ?? undefined;
    return message;
  },
};

function createBaseSubscribeRequestFilterTransactions(): SubscribeRequestFilterTransactions {
  return {
    vote: undefined,
    failed: undefined,
    signature: undefined,
    accountInclude: [],
    accountExclude: [],
    accountRequired: [],
  };
}

export const SubscribeRequestFilterTransactions: MessageFns<SubscribeRequestFilterTransactions> = {
  encode(message: SubscribeRequestFilterTransactions, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.vote !== undefined) {
      writer.uint32(8).bool(message.vote);
    }
    if (message.failed !== undefined) {
      writer.uint32(16).bool(message.failed);
    }
    if (message.signature !== undefined) {
      writer.uint32(42).string(message.signature);
    }
    for (const v of message.accountInclude) {
      writer.uint32(26).string(v!);
    }
    for (const v of message.accountExclude) {
      writer.uint32(34).string(v!);
    }
    for (const v of message.accountRequired) {
      writer.uint32(50).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterTransactions {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterTransactions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.vote = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.failed = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.signature = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.accountInclude.push(reader.string());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.accountExclude.push(reader.string());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.accountRequired.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterTransactions, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterTransactions | SubscribeRequestFilterTransactions[]>
      | Iterable<SubscribeRequestFilterTransactions | SubscribeRequestFilterTransactions[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterTransactions.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterTransactions.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterTransactions>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterTransactions> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterTransactions.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterTransactions.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterTransactions {
    return {
      vote: isSet(object.vote) ? globalThis.Boolean(object.vote) : undefined,
      failed: isSet(object.failed) ? globalThis.Boolean(object.failed) : undefined,
      signature: isSet(object.signature) ? globalThis.String(object.signature) : undefined,
      accountInclude: globalThis.Array.isArray(object?.accountInclude)
        ? object.accountInclude.map((e: any) => globalThis.String(e))
        : [],
      accountExclude: globalThis.Array.isArray(object?.accountExclude)
        ? object.accountExclude.map((e: any) => globalThis.String(e))
        : [],
      accountRequired: globalThis.Array.isArray(object?.accountRequired)
        ? object.accountRequired.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: SubscribeRequestFilterTransactions): unknown {
    const obj: any = {};
    if (message.vote !== undefined) {
      obj.vote = message.vote;
    }
    if (message.failed !== undefined) {
      obj.failed = message.failed;
    }
    if (message.signature !== undefined) {
      obj.signature = message.signature;
    }
    if (message.accountInclude?.length) {
      obj.accountInclude = message.accountInclude;
    }
    if (message.accountExclude?.length) {
      obj.accountExclude = message.accountExclude;
    }
    if (message.accountRequired?.length) {
      obj.accountRequired = message.accountRequired;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterTransactions>, I>>(
    base?: I,
  ): SubscribeRequestFilterTransactions {
    return SubscribeRequestFilterTransactions.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterTransactions>, I>>(
    object: I,
  ): SubscribeRequestFilterTransactions {
    const message = createBaseSubscribeRequestFilterTransactions();
    message.vote = object.vote ?? undefined;
    message.failed = object.failed ?? undefined;
    message.signature = object.signature ?? undefined;
    message.accountInclude = object.accountInclude?.map((e) => e) || [];
    message.accountExclude = object.accountExclude?.map((e) => e) || [];
    message.accountRequired = object.accountRequired?.map((e) => e) || [];
    return message;
  },
};

function createBaseSubscribeRequestFilterBlocks(): SubscribeRequestFilterBlocks {
  return { accountInclude: [], includeTransactions: undefined, includeAccounts: undefined, includeEntries: undefined };
}

export const SubscribeRequestFilterBlocks: MessageFns<SubscribeRequestFilterBlocks> = {
  encode(message: SubscribeRequestFilterBlocks, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.accountInclude) {
      writer.uint32(10).string(v!);
    }
    if (message.includeTransactions !== undefined) {
      writer.uint32(16).bool(message.includeTransactions);
    }
    if (message.includeAccounts !== undefined) {
      writer.uint32(24).bool(message.includeAccounts);
    }
    if (message.includeEntries !== undefined) {
      writer.uint32(32).bool(message.includeEntries);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterBlocks {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterBlocks();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountInclude.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.includeTransactions = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.includeAccounts = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.includeEntries = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterBlocks, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterBlocks | SubscribeRequestFilterBlocks[]>
      | Iterable<SubscribeRequestFilterBlocks | SubscribeRequestFilterBlocks[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterBlocks.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterBlocks.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterBlocks>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterBlocks> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterBlocks.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterBlocks.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestFilterBlocks {
    return {
      accountInclude: globalThis.Array.isArray(object?.accountInclude)
        ? object.accountInclude.map((e: any) => globalThis.String(e))
        : [],
      includeTransactions: isSet(object.includeTransactions)
        ? globalThis.Boolean(object.includeTransactions)
        : undefined,
      includeAccounts: isSet(object.includeAccounts) ? globalThis.Boolean(object.includeAccounts) : undefined,
      includeEntries: isSet(object.includeEntries) ? globalThis.Boolean(object.includeEntries) : undefined,
    };
  },

  toJSON(message: SubscribeRequestFilterBlocks): unknown {
    const obj: any = {};
    if (message.accountInclude?.length) {
      obj.accountInclude = message.accountInclude;
    }
    if (message.includeTransactions !== undefined) {
      obj.includeTransactions = message.includeTransactions;
    }
    if (message.includeAccounts !== undefined) {
      obj.includeAccounts = message.includeAccounts;
    }
    if (message.includeEntries !== undefined) {
      obj.includeEntries = message.includeEntries;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterBlocks>, I>>(base?: I): SubscribeRequestFilterBlocks {
    return SubscribeRequestFilterBlocks.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterBlocks>, I>>(object: I): SubscribeRequestFilterBlocks {
    const message = createBaseSubscribeRequestFilterBlocks();
    message.accountInclude = object.accountInclude?.map((e) => e) || [];
    message.includeTransactions = object.includeTransactions ?? undefined;
    message.includeAccounts = object.includeAccounts ?? undefined;
    message.includeEntries = object.includeEntries ?? undefined;
    return message;
  },
};

function createBaseSubscribeRequestFilterBlocksMeta(): SubscribeRequestFilterBlocksMeta {
  return {};
}

export const SubscribeRequestFilterBlocksMeta: MessageFns<SubscribeRequestFilterBlocksMeta> = {
  encode(_: SubscribeRequestFilterBlocksMeta, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterBlocksMeta {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterBlocksMeta();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterBlocksMeta, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterBlocksMeta | SubscribeRequestFilterBlocksMeta[]>
      | Iterable<SubscribeRequestFilterBlocksMeta | SubscribeRequestFilterBlocksMeta[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterBlocksMeta.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterBlocksMeta.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterBlocksMeta>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterBlocksMeta> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterBlocksMeta.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterBlocksMeta.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): SubscribeRequestFilterBlocksMeta {
    return {};
  },

  toJSON(_: SubscribeRequestFilterBlocksMeta): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterBlocksMeta>, I>>(
    base?: I,
  ): SubscribeRequestFilterBlocksMeta {
    return SubscribeRequestFilterBlocksMeta.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterBlocksMeta>, I>>(
    _: I,
  ): SubscribeRequestFilterBlocksMeta {
    const message = createBaseSubscribeRequestFilterBlocksMeta();
    return message;
  },
};

function createBaseSubscribeRequestFilterEntry(): SubscribeRequestFilterEntry {
  return {};
}

export const SubscribeRequestFilterEntry: MessageFns<SubscribeRequestFilterEntry> = {
  encode(_: SubscribeRequestFilterEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestFilterEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestFilterEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestFilterEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestFilterEntry | SubscribeRequestFilterEntry[]>
      | Iterable<SubscribeRequestFilterEntry | SubscribeRequestFilterEntry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterEntry.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestFilterEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestFilterEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestFilterEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestFilterEntry.decode(p)];
        }
      } else {
        yield* [SubscribeRequestFilterEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): SubscribeRequestFilterEntry {
    return {};
  },

  toJSON(_: SubscribeRequestFilterEntry): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestFilterEntry>, I>>(base?: I): SubscribeRequestFilterEntry {
    return SubscribeRequestFilterEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestFilterEntry>, I>>(_: I): SubscribeRequestFilterEntry {
    const message = createBaseSubscribeRequestFilterEntry();
    return message;
  },
};

function createBaseSubscribeRequestAccountsDataSlice(): SubscribeRequestAccountsDataSlice {
  return { offset: 0n, length: 0n };
}

export const SubscribeRequestAccountsDataSlice: MessageFns<SubscribeRequestAccountsDataSlice> = {
  encode(message: SubscribeRequestAccountsDataSlice, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.offset !== 0n) {
      if (BigInt.asUintN(64, message.offset) !== message.offset) {
        throw new globalThis.Error("value provided for field message.offset of type uint64 too large");
      }
      writer.uint32(8).uint64(message.offset);
    }
    if (message.length !== 0n) {
      if (BigInt.asUintN(64, message.length) !== message.length) {
        throw new globalThis.Error("value provided for field message.length of type uint64 too large");
      }
      writer.uint32(16).uint64(message.length);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestAccountsDataSlice {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestAccountsDataSlice();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.offset = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.length = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestAccountsDataSlice, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestAccountsDataSlice | SubscribeRequestAccountsDataSlice[]>
      | Iterable<SubscribeRequestAccountsDataSlice | SubscribeRequestAccountsDataSlice[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestAccountsDataSlice.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestAccountsDataSlice.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestAccountsDataSlice>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestAccountsDataSlice> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestAccountsDataSlice.decode(p)];
        }
      } else {
        yield* [SubscribeRequestAccountsDataSlice.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestAccountsDataSlice {
    return {
      offset: isSet(object.offset) ? BigInt(object.offset) : 0n,
      length: isSet(object.length) ? BigInt(object.length) : 0n,
    };
  },

  toJSON(message: SubscribeRequestAccountsDataSlice): unknown {
    const obj: any = {};
    if (message.offset !== 0n) {
      obj.offset = message.offset.toString();
    }
    if (message.length !== 0n) {
      obj.length = message.length.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestAccountsDataSlice>, I>>(
    base?: I,
  ): SubscribeRequestAccountsDataSlice {
    return SubscribeRequestAccountsDataSlice.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestAccountsDataSlice>, I>>(
    object: I,
  ): SubscribeRequestAccountsDataSlice {
    const message = createBaseSubscribeRequestAccountsDataSlice();
    message.offset = object.offset ?? 0n;
    message.length = object.length ?? 0n;
    return message;
  },
};

function createBaseSubscribeRequestPing(): SubscribeRequestPing {
  return { id: 0 };
}

export const SubscribeRequestPing: MessageFns<SubscribeRequestPing> = {
  encode(message: SubscribeRequestPing, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int32(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequestPing {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequestPing();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequestPing, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeRequestPing | SubscribeRequestPing[]>
      | Iterable<SubscribeRequestPing | SubscribeRequestPing[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestPing.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequestPing.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequestPing>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequestPing> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequestPing.decode(p)];
        }
      } else {
        yield* [SubscribeRequestPing.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeRequestPing {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  toJSON(message: SubscribeRequestPing): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequestPing>, I>>(base?: I): SubscribeRequestPing {
    return SubscribeRequestPing.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequestPing>, I>>(object: I): SubscribeRequestPing {
    const message = createBaseSubscribeRequestPing();
    message.id = object.id ?? 0;
    return message;
  },
};

function createBaseSubscribeUpdate(): SubscribeUpdate {
  return {
    filters: [],
    account: undefined,
    slot: undefined,
    transaction: undefined,
    transactionStatus: undefined,
    block: undefined,
    ping: undefined,
    pong: undefined,
    blockMeta: undefined,
    entry: undefined,
    createdAt: undefined,
  };
}

export const SubscribeUpdate: MessageFns<SubscribeUpdate> = {
  encode(message: SubscribeUpdate, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.filters) {
      writer.uint32(10).string(v!);
    }
    if (message.account !== undefined) {
      SubscribeUpdateAccount.encode(message.account, writer.uint32(18).fork()).join();
    }
    if (message.slot !== undefined) {
      SubscribeUpdateSlot.encode(message.slot, writer.uint32(26).fork()).join();
    }
    if (message.transaction !== undefined) {
      SubscribeUpdateTransaction.encode(message.transaction, writer.uint32(34).fork()).join();
    }
    if (message.transactionStatus !== undefined) {
      SubscribeUpdateTransactionStatus.encode(message.transactionStatus, writer.uint32(82).fork()).join();
    }
    if (message.block !== undefined) {
      SubscribeUpdateBlock.encode(message.block, writer.uint32(42).fork()).join();
    }
    if (message.ping !== undefined) {
      SubscribeUpdatePing.encode(message.ping, writer.uint32(50).fork()).join();
    }
    if (message.pong !== undefined) {
      SubscribeUpdatePong.encode(message.pong, writer.uint32(74).fork()).join();
    }
    if (message.blockMeta !== undefined) {
      SubscribeUpdateBlockMeta.encode(message.blockMeta, writer.uint32(58).fork()).join();
    }
    if (message.entry !== undefined) {
      SubscribeUpdateEntry.encode(message.entry, writer.uint32(66).fork()).join();
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(90).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdate {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.filters.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.account = SubscribeUpdateAccount.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.slot = SubscribeUpdateSlot.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.transaction = SubscribeUpdateTransaction.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.transactionStatus = SubscribeUpdateTransactionStatus.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.block = SubscribeUpdateBlock.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.ping = SubscribeUpdatePing.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.pong = SubscribeUpdatePong.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.blockMeta = SubscribeUpdateBlockMeta.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.entry = SubscribeUpdateEntry.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdate, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<SubscribeUpdate | SubscribeUpdate[]> | Iterable<SubscribeUpdate | SubscribeUpdate[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdate.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdate.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdate>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdate> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdate.decode(p)];
        }
      } else {
        yield* [SubscribeUpdate.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdate {
    return {
      filters: globalThis.Array.isArray(object?.filters) ? object.filters.map((e: any) => globalThis.String(e)) : [],
      account: isSet(object.account) ? SubscribeUpdateAccount.fromJSON(object.account) : undefined,
      slot: isSet(object.slot) ? SubscribeUpdateSlot.fromJSON(object.slot) : undefined,
      transaction: isSet(object.transaction) ? SubscribeUpdateTransaction.fromJSON(object.transaction) : undefined,
      transactionStatus: isSet(object.transactionStatus)
        ? SubscribeUpdateTransactionStatus.fromJSON(object.transactionStatus)
        : undefined,
      block: isSet(object.block) ? SubscribeUpdateBlock.fromJSON(object.block) : undefined,
      ping: isSet(object.ping) ? SubscribeUpdatePing.fromJSON(object.ping) : undefined,
      pong: isSet(object.pong) ? SubscribeUpdatePong.fromJSON(object.pong) : undefined,
      blockMeta: isSet(object.blockMeta) ? SubscribeUpdateBlockMeta.fromJSON(object.blockMeta) : undefined,
      entry: isSet(object.entry) ? SubscribeUpdateEntry.fromJSON(object.entry) : undefined,
      createdAt: isSet(object.createdAt) ? fromJsonTimestamp(object.createdAt) : undefined,
    };
  },

  toJSON(message: SubscribeUpdate): unknown {
    const obj: any = {};
    if (message.filters?.length) {
      obj.filters = message.filters;
    }
    if (message.account !== undefined) {
      obj.account = SubscribeUpdateAccount.toJSON(message.account);
    }
    if (message.slot !== undefined) {
      obj.slot = SubscribeUpdateSlot.toJSON(message.slot);
    }
    if (message.transaction !== undefined) {
      obj.transaction = SubscribeUpdateTransaction.toJSON(message.transaction);
    }
    if (message.transactionStatus !== undefined) {
      obj.transactionStatus = SubscribeUpdateTransactionStatus.toJSON(message.transactionStatus);
    }
    if (message.block !== undefined) {
      obj.block = SubscribeUpdateBlock.toJSON(message.block);
    }
    if (message.ping !== undefined) {
      obj.ping = SubscribeUpdatePing.toJSON(message.ping);
    }
    if (message.pong !== undefined) {
      obj.pong = SubscribeUpdatePong.toJSON(message.pong);
    }
    if (message.blockMeta !== undefined) {
      obj.blockMeta = SubscribeUpdateBlockMeta.toJSON(message.blockMeta);
    }
    if (message.entry !== undefined) {
      obj.entry = SubscribeUpdateEntry.toJSON(message.entry);
    }
    if (message.createdAt !== undefined) {
      obj.createdAt = message.createdAt.toISOString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdate>, I>>(base?: I): SubscribeUpdate {
    return SubscribeUpdate.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdate>, I>>(object: I): SubscribeUpdate {
    const message = createBaseSubscribeUpdate();
    message.filters = object.filters?.map((e) => e) || [];
    message.account = (object.account !== undefined && object.account !== null)
      ? SubscribeUpdateAccount.fromPartial(object.account)
      : undefined;
    message.slot = (object.slot !== undefined && object.slot !== null)
      ? SubscribeUpdateSlot.fromPartial(object.slot)
      : undefined;
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? SubscribeUpdateTransaction.fromPartial(object.transaction)
      : undefined;
    message.transactionStatus = (object.transactionStatus !== undefined && object.transactionStatus !== null)
      ? SubscribeUpdateTransactionStatus.fromPartial(object.transactionStatus)
      : undefined;
    message.block = (object.block !== undefined && object.block !== null)
      ? SubscribeUpdateBlock.fromPartial(object.block)
      : undefined;
    message.ping = (object.ping !== undefined && object.ping !== null)
      ? SubscribeUpdatePing.fromPartial(object.ping)
      : undefined;
    message.pong = (object.pong !== undefined && object.pong !== null)
      ? SubscribeUpdatePong.fromPartial(object.pong)
      : undefined;
    message.blockMeta = (object.blockMeta !== undefined && object.blockMeta !== null)
      ? SubscribeUpdateBlockMeta.fromPartial(object.blockMeta)
      : undefined;
    message.entry = (object.entry !== undefined && object.entry !== null)
      ? SubscribeUpdateEntry.fromPartial(object.entry)
      : undefined;
    message.createdAt = object.createdAt ?? undefined;
    return message;
  },
};

function createBaseSubscribeUpdateAccount(): SubscribeUpdateAccount {
  return { account: undefined, slot: 0n, isStartup: false };
}

export const SubscribeUpdateAccount: MessageFns<SubscribeUpdateAccount> = {
  encode(message: SubscribeUpdateAccount, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.account !== undefined) {
      SubscribeUpdateAccountInfo.encode(message.account, writer.uint32(10).fork()).join();
    }
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(16).uint64(message.slot);
    }
    if (message.isStartup !== false) {
      writer.uint32(24).bool(message.isStartup);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateAccount {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateAccount();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.account = SubscribeUpdateAccountInfo.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isStartup = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateAccount, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateAccount | SubscribeUpdateAccount[]>
      | Iterable<SubscribeUpdateAccount | SubscribeUpdateAccount[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateAccount.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateAccount.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateAccount>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateAccount> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateAccount.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateAccount.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateAccount {
    return {
      account: isSet(object.account) ? SubscribeUpdateAccountInfo.fromJSON(object.account) : undefined,
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      isStartup: isSet(object.isStartup) ? globalThis.Boolean(object.isStartup) : false,
    };
  },

  toJSON(message: SubscribeUpdateAccount): unknown {
    const obj: any = {};
    if (message.account !== undefined) {
      obj.account = SubscribeUpdateAccountInfo.toJSON(message.account);
    }
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.isStartup !== false) {
      obj.isStartup = message.isStartup;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateAccount>, I>>(base?: I): SubscribeUpdateAccount {
    return SubscribeUpdateAccount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateAccount>, I>>(object: I): SubscribeUpdateAccount {
    const message = createBaseSubscribeUpdateAccount();
    message.account = (object.account !== undefined && object.account !== null)
      ? SubscribeUpdateAccountInfo.fromPartial(object.account)
      : undefined;
    message.slot = object.slot ?? 0n;
    message.isStartup = object.isStartup ?? false;
    return message;
  },
};

function createBaseSubscribeUpdateAccountInfo(): SubscribeUpdateAccountInfo {
  return {
    pubkey: Buffer.alloc(0),
    lamports: 0n,
    owner: Buffer.alloc(0),
    executable: false,
    rentEpoch: 0n,
    data: Buffer.alloc(0),
    writeVersion: 0n,
    txnSignature: undefined,
  };
}

export const SubscribeUpdateAccountInfo: MessageFns<SubscribeUpdateAccountInfo> = {
  encode(message: SubscribeUpdateAccountInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pubkey.length !== 0) {
      writer.uint32(10).bytes(message.pubkey);
    }
    if (message.lamports !== 0n) {
      if (BigInt.asUintN(64, message.lamports) !== message.lamports) {
        throw new globalThis.Error("value provided for field message.lamports of type uint64 too large");
      }
      writer.uint32(16).uint64(message.lamports);
    }
    if (message.owner.length !== 0) {
      writer.uint32(26).bytes(message.owner);
    }
    if (message.executable !== false) {
      writer.uint32(32).bool(message.executable);
    }
    if (message.rentEpoch !== 0n) {
      if (BigInt.asUintN(64, message.rentEpoch) !== message.rentEpoch) {
        throw new globalThis.Error("value provided for field message.rentEpoch of type uint64 too large");
      }
      writer.uint32(40).uint64(message.rentEpoch);
    }
    if (message.data.length !== 0) {
      writer.uint32(50).bytes(message.data);
    }
    if (message.writeVersion !== 0n) {
      if (BigInt.asUintN(64, message.writeVersion) !== message.writeVersion) {
        throw new globalThis.Error("value provided for field message.writeVersion of type uint64 too large");
      }
      writer.uint32(56).uint64(message.writeVersion);
    }
    if (message.txnSignature !== undefined) {
      writer.uint32(66).bytes(message.txnSignature);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateAccountInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateAccountInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pubkey = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lamports = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.owner = Buffer.from(reader.bytes());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.executable = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.rentEpoch = reader.uint64() as bigint;
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.data = Buffer.from(reader.bytes());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.writeVersion = reader.uint64() as bigint;
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.txnSignature = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateAccountInfo, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateAccountInfo | SubscribeUpdateAccountInfo[]>
      | Iterable<SubscribeUpdateAccountInfo | SubscribeUpdateAccountInfo[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateAccountInfo.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateAccountInfo.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateAccountInfo>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateAccountInfo> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateAccountInfo.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateAccountInfo.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateAccountInfo {
    return {
      pubkey: isSet(object.pubkey) ? Buffer.from(bytesFromBase64(object.pubkey)) : Buffer.alloc(0),
      lamports: isSet(object.lamports) ? BigInt(object.lamports) : 0n,
      owner: isSet(object.owner) ? Buffer.from(bytesFromBase64(object.owner)) : Buffer.alloc(0),
      executable: isSet(object.executable) ? globalThis.Boolean(object.executable) : false,
      rentEpoch: isSet(object.rentEpoch) ? BigInt(object.rentEpoch) : 0n,
      data: isSet(object.data) ? Buffer.from(bytesFromBase64(object.data)) : Buffer.alloc(0),
      writeVersion: isSet(object.writeVersion) ? BigInt(object.writeVersion) : 0n,
      txnSignature: isSet(object.txnSignature) ? Buffer.from(bytesFromBase64(object.txnSignature)) : undefined,
    };
  },

  toJSON(message: SubscribeUpdateAccountInfo): unknown {
    const obj: any = {};
    if (message.pubkey.length !== 0) {
      obj.pubkey = base64FromBytes(message.pubkey);
    }
    if (message.lamports !== 0n) {
      obj.lamports = message.lamports.toString();
    }
    if (message.owner.length !== 0) {
      obj.owner = base64FromBytes(message.owner);
    }
    if (message.executable !== false) {
      obj.executable = message.executable;
    }
    if (message.rentEpoch !== 0n) {
      obj.rentEpoch = message.rentEpoch.toString();
    }
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    if (message.writeVersion !== 0n) {
      obj.writeVersion = message.writeVersion.toString();
    }
    if (message.txnSignature !== undefined) {
      obj.txnSignature = base64FromBytes(message.txnSignature);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateAccountInfo>, I>>(base?: I): SubscribeUpdateAccountInfo {
    return SubscribeUpdateAccountInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateAccountInfo>, I>>(object: I): SubscribeUpdateAccountInfo {
    const message = createBaseSubscribeUpdateAccountInfo();
    message.pubkey = object.pubkey ?? Buffer.alloc(0);
    message.lamports = object.lamports ?? 0n;
    message.owner = object.owner ?? Buffer.alloc(0);
    message.executable = object.executable ?? false;
    message.rentEpoch = object.rentEpoch ?? 0n;
    message.data = object.data ?? Buffer.alloc(0);
    message.writeVersion = object.writeVersion ?? 0n;
    message.txnSignature = object.txnSignature ?? undefined;
    return message;
  },
};

function createBaseSubscribeUpdateSlot(): SubscribeUpdateSlot {
  return { slot: 0n, parent: undefined, status: 0, deadError: undefined };
}

export const SubscribeUpdateSlot: MessageFns<SubscribeUpdateSlot> = {
  encode(message: SubscribeUpdateSlot, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.parent !== undefined) {
      if (BigInt.asUintN(64, message.parent) !== message.parent) {
        throw new globalThis.Error("value provided for field message.parent of type uint64 too large");
      }
      writer.uint32(16).uint64(message.parent);
    }
    if (message.status !== 0) {
      writer.uint32(24).int32(message.status);
    }
    if (message.deadError !== undefined) {
      writer.uint32(34).string(message.deadError);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateSlot {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateSlot();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.parent = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.status = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.deadError = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateSlot, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateSlot | SubscribeUpdateSlot[]>
      | Iterable<SubscribeUpdateSlot | SubscribeUpdateSlot[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateSlot.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateSlot.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateSlot>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateSlot> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateSlot.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateSlot.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateSlot {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      parent: isSet(object.parent) ? BigInt(object.parent) : undefined,
      status: isSet(object.status) ? slotStatusFromJSON(object.status) : 0,
      deadError: isSet(object.deadError) ? globalThis.String(object.deadError) : undefined,
    };
  },

  toJSON(message: SubscribeUpdateSlot): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.parent !== undefined) {
      obj.parent = message.parent.toString();
    }
    if (message.status !== 0) {
      obj.status = slotStatusToJSON(message.status);
    }
    if (message.deadError !== undefined) {
      obj.deadError = message.deadError;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateSlot>, I>>(base?: I): SubscribeUpdateSlot {
    return SubscribeUpdateSlot.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateSlot>, I>>(object: I): SubscribeUpdateSlot {
    const message = createBaseSubscribeUpdateSlot();
    message.slot = object.slot ?? 0n;
    message.parent = object.parent ?? undefined;
    message.status = object.status ?? 0;
    message.deadError = object.deadError ?? undefined;
    return message;
  },
};

function createBaseSubscribeUpdateTransaction(): SubscribeUpdateTransaction {
  return { transaction: undefined, slot: 0n };
}

export const SubscribeUpdateTransaction: MessageFns<SubscribeUpdateTransaction> = {
  encode(message: SubscribeUpdateTransaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.transaction !== undefined) {
      SubscribeUpdateTransactionInfo.encode(message.transaction, writer.uint32(10).fork()).join();
    }
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(16).uint64(message.slot);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateTransaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transaction = SubscribeUpdateTransactionInfo.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateTransaction, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateTransaction | SubscribeUpdateTransaction[]>
      | Iterable<SubscribeUpdateTransaction | SubscribeUpdateTransaction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateTransaction.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateTransaction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateTransaction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateTransaction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateTransaction.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateTransaction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateTransaction {
    return {
      transaction: isSet(object.transaction) ? SubscribeUpdateTransactionInfo.fromJSON(object.transaction) : undefined,
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
    };
  },

  toJSON(message: SubscribeUpdateTransaction): unknown {
    const obj: any = {};
    if (message.transaction !== undefined) {
      obj.transaction = SubscribeUpdateTransactionInfo.toJSON(message.transaction);
    }
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateTransaction>, I>>(base?: I): SubscribeUpdateTransaction {
    return SubscribeUpdateTransaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateTransaction>, I>>(object: I): SubscribeUpdateTransaction {
    const message = createBaseSubscribeUpdateTransaction();
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? SubscribeUpdateTransactionInfo.fromPartial(object.transaction)
      : undefined;
    message.slot = object.slot ?? 0n;
    return message;
  },
};

function createBaseSubscribeUpdateTransactionInfo(): SubscribeUpdateTransactionInfo {
  return { signature: Buffer.alloc(0), isVote: false, transaction: undefined, meta: undefined, index: 0n };
}

export const SubscribeUpdateTransactionInfo: MessageFns<SubscribeUpdateTransactionInfo> = {
  encode(message: SubscribeUpdateTransactionInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.signature.length !== 0) {
      writer.uint32(10).bytes(message.signature);
    }
    if (message.isVote !== false) {
      writer.uint32(16).bool(message.isVote);
    }
    if (message.transaction !== undefined) {
      Transaction.encode(message.transaction, writer.uint32(26).fork()).join();
    }
    if (message.meta !== undefined) {
      TransactionStatusMeta.encode(message.meta, writer.uint32(34).fork()).join();
    }
    if (message.index !== 0n) {
      if (BigInt.asUintN(64, message.index) !== message.index) {
        throw new globalThis.Error("value provided for field message.index of type uint64 too large");
      }
      writer.uint32(40).uint64(message.index);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateTransactionInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateTransactionInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.signature = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isVote = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.transaction = Transaction.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.meta = TransactionStatusMeta.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.index = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateTransactionInfo, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateTransactionInfo | SubscribeUpdateTransactionInfo[]>
      | Iterable<SubscribeUpdateTransactionInfo | SubscribeUpdateTransactionInfo[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateTransactionInfo.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateTransactionInfo.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateTransactionInfo>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateTransactionInfo> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateTransactionInfo.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateTransactionInfo.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateTransactionInfo {
    return {
      signature: isSet(object.signature) ? Buffer.from(bytesFromBase64(object.signature)) : Buffer.alloc(0),
      isVote: isSet(object.isVote) ? globalThis.Boolean(object.isVote) : false,
      transaction: isSet(object.transaction) ? Transaction.fromJSON(object.transaction) : undefined,
      meta: isSet(object.meta) ? TransactionStatusMeta.fromJSON(object.meta) : undefined,
      index: isSet(object.index) ? BigInt(object.index) : 0n,
    };
  },

  toJSON(message: SubscribeUpdateTransactionInfo): unknown {
    const obj: any = {};
    if (message.signature.length !== 0) {
      obj.signature = base64FromBytes(message.signature);
    }
    if (message.isVote !== false) {
      obj.isVote = message.isVote;
    }
    if (message.transaction !== undefined) {
      obj.transaction = Transaction.toJSON(message.transaction);
    }
    if (message.meta !== undefined) {
      obj.meta = TransactionStatusMeta.toJSON(message.meta);
    }
    if (message.index !== 0n) {
      obj.index = message.index.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateTransactionInfo>, I>>(base?: I): SubscribeUpdateTransactionInfo {
    return SubscribeUpdateTransactionInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateTransactionInfo>, I>>(
    object: I,
  ): SubscribeUpdateTransactionInfo {
    const message = createBaseSubscribeUpdateTransactionInfo();
    message.signature = object.signature ?? Buffer.alloc(0);
    message.isVote = object.isVote ?? false;
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? Transaction.fromPartial(object.transaction)
      : undefined;
    message.meta = (object.meta !== undefined && object.meta !== null)
      ? TransactionStatusMeta.fromPartial(object.meta)
      : undefined;
    message.index = object.index ?? 0n;
    return message;
  },
};

function createBaseSubscribeUpdateTransactionStatus(): SubscribeUpdateTransactionStatus {
  return { slot: 0n, signature: Buffer.alloc(0), isVote: false, index: 0n, err: undefined };
}

export const SubscribeUpdateTransactionStatus: MessageFns<SubscribeUpdateTransactionStatus> = {
  encode(message: SubscribeUpdateTransactionStatus, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.signature.length !== 0) {
      writer.uint32(18).bytes(message.signature);
    }
    if (message.isVote !== false) {
      writer.uint32(24).bool(message.isVote);
    }
    if (message.index !== 0n) {
      if (BigInt.asUintN(64, message.index) !== message.index) {
        throw new globalThis.Error("value provided for field message.index of type uint64 too large");
      }
      writer.uint32(32).uint64(message.index);
    }
    if (message.err !== undefined) {
      TransactionError.encode(message.err, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateTransactionStatus {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateTransactionStatus();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.signature = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isVote = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.index = reader.uint64() as bigint;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.err = TransactionError.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateTransactionStatus, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateTransactionStatus | SubscribeUpdateTransactionStatus[]>
      | Iterable<SubscribeUpdateTransactionStatus | SubscribeUpdateTransactionStatus[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateTransactionStatus.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateTransactionStatus.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateTransactionStatus>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateTransactionStatus> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateTransactionStatus.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateTransactionStatus.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateTransactionStatus {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      signature: isSet(object.signature) ? Buffer.from(bytesFromBase64(object.signature)) : Buffer.alloc(0),
      isVote: isSet(object.isVote) ? globalThis.Boolean(object.isVote) : false,
      index: isSet(object.index) ? BigInt(object.index) : 0n,
      err: isSet(object.err) ? TransactionError.fromJSON(object.err) : undefined,
    };
  },

  toJSON(message: SubscribeUpdateTransactionStatus): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.signature.length !== 0) {
      obj.signature = base64FromBytes(message.signature);
    }
    if (message.isVote !== false) {
      obj.isVote = message.isVote;
    }
    if (message.index !== 0n) {
      obj.index = message.index.toString();
    }
    if (message.err !== undefined) {
      obj.err = TransactionError.toJSON(message.err);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateTransactionStatus>, I>>(
    base?: I,
  ): SubscribeUpdateTransactionStatus {
    return SubscribeUpdateTransactionStatus.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateTransactionStatus>, I>>(
    object: I,
  ): SubscribeUpdateTransactionStatus {
    const message = createBaseSubscribeUpdateTransactionStatus();
    message.slot = object.slot ?? 0n;
    message.signature = object.signature ?? Buffer.alloc(0);
    message.isVote = object.isVote ?? false;
    message.index = object.index ?? 0n;
    message.err = (object.err !== undefined && object.err !== null)
      ? TransactionError.fromPartial(object.err)
      : undefined;
    return message;
  },
};

function createBaseSubscribeUpdateBlock(): SubscribeUpdateBlock {
  return {
    slot: 0n,
    blockhash: "",
    rewards: undefined,
    blockTime: undefined,
    blockHeight: undefined,
    parentSlot: 0n,
    parentBlockhash: "",
    executedTransactionCount: 0n,
    transactions: [],
    updatedAccountCount: 0n,
    accounts: [],
    entriesCount: 0n,
    entries: [],
  };
}

export const SubscribeUpdateBlock: MessageFns<SubscribeUpdateBlock> = {
  encode(message: SubscribeUpdateBlock, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.blockhash !== "") {
      writer.uint32(18).string(message.blockhash);
    }
    if (message.rewards !== undefined) {
      Rewards.encode(message.rewards, writer.uint32(26).fork()).join();
    }
    if (message.blockTime !== undefined) {
      UnixTimestamp.encode(message.blockTime, writer.uint32(34).fork()).join();
    }
    if (message.blockHeight !== undefined) {
      BlockHeight.encode(message.blockHeight, writer.uint32(42).fork()).join();
    }
    if (message.parentSlot !== 0n) {
      if (BigInt.asUintN(64, message.parentSlot) !== message.parentSlot) {
        throw new globalThis.Error("value provided for field message.parentSlot of type uint64 too large");
      }
      writer.uint32(56).uint64(message.parentSlot);
    }
    if (message.parentBlockhash !== "") {
      writer.uint32(66).string(message.parentBlockhash);
    }
    if (message.executedTransactionCount !== 0n) {
      if (BigInt.asUintN(64, message.executedTransactionCount) !== message.executedTransactionCount) {
        throw new globalThis.Error(
          "value provided for field message.executedTransactionCount of type uint64 too large",
        );
      }
      writer.uint32(72).uint64(message.executedTransactionCount);
    }
    for (const v of message.transactions) {
      SubscribeUpdateTransactionInfo.encode(v!, writer.uint32(50).fork()).join();
    }
    if (message.updatedAccountCount !== 0n) {
      if (BigInt.asUintN(64, message.updatedAccountCount) !== message.updatedAccountCount) {
        throw new globalThis.Error("value provided for field message.updatedAccountCount of type uint64 too large");
      }
      writer.uint32(80).uint64(message.updatedAccountCount);
    }
    for (const v of message.accounts) {
      SubscribeUpdateAccountInfo.encode(v!, writer.uint32(90).fork()).join();
    }
    if (message.entriesCount !== 0n) {
      if (BigInt.asUintN(64, message.entriesCount) !== message.entriesCount) {
        throw new globalThis.Error("value provided for field message.entriesCount of type uint64 too large");
      }
      writer.uint32(96).uint64(message.entriesCount);
    }
    for (const v of message.entries) {
      SubscribeUpdateEntry.encode(v!, writer.uint32(106).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateBlock {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateBlock();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.blockhash = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rewards = Rewards.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.blockTime = UnixTimestamp.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.blockHeight = BlockHeight.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.parentSlot = reader.uint64() as bigint;
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.parentBlockhash = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.executedTransactionCount = reader.uint64() as bigint;
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.transactions.push(SubscribeUpdateTransactionInfo.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.updatedAccountCount = reader.uint64() as bigint;
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.accounts.push(SubscribeUpdateAccountInfo.decode(reader, reader.uint32()));
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.entriesCount = reader.uint64() as bigint;
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.entries.push(SubscribeUpdateEntry.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateBlock, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateBlock | SubscribeUpdateBlock[]>
      | Iterable<SubscribeUpdateBlock | SubscribeUpdateBlock[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateBlock.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateBlock.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateBlock>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateBlock> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateBlock.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateBlock.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateBlock {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      blockhash: isSet(object.blockhash) ? globalThis.String(object.blockhash) : "",
      rewards: isSet(object.rewards) ? Rewards.fromJSON(object.rewards) : undefined,
      blockTime: isSet(object.blockTime) ? UnixTimestamp.fromJSON(object.blockTime) : undefined,
      blockHeight: isSet(object.blockHeight) ? BlockHeight.fromJSON(object.blockHeight) : undefined,
      parentSlot: isSet(object.parentSlot) ? BigInt(object.parentSlot) : 0n,
      parentBlockhash: isSet(object.parentBlockhash) ? globalThis.String(object.parentBlockhash) : "",
      executedTransactionCount: isSet(object.executedTransactionCount) ? BigInt(object.executedTransactionCount) : 0n,
      transactions: globalThis.Array.isArray(object?.transactions)
        ? object.transactions.map((e: any) => SubscribeUpdateTransactionInfo.fromJSON(e))
        : [],
      updatedAccountCount: isSet(object.updatedAccountCount) ? BigInt(object.updatedAccountCount) : 0n,
      accounts: globalThis.Array.isArray(object?.accounts)
        ? object.accounts.map((e: any) => SubscribeUpdateAccountInfo.fromJSON(e))
        : [],
      entriesCount: isSet(object.entriesCount) ? BigInt(object.entriesCount) : 0n,
      entries: globalThis.Array.isArray(object?.entries)
        ? object.entries.map((e: any) => SubscribeUpdateEntry.fromJSON(e))
        : [],
    };
  },

  toJSON(message: SubscribeUpdateBlock): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.blockhash !== "") {
      obj.blockhash = message.blockhash;
    }
    if (message.rewards !== undefined) {
      obj.rewards = Rewards.toJSON(message.rewards);
    }
    if (message.blockTime !== undefined) {
      obj.blockTime = UnixTimestamp.toJSON(message.blockTime);
    }
    if (message.blockHeight !== undefined) {
      obj.blockHeight = BlockHeight.toJSON(message.blockHeight);
    }
    if (message.parentSlot !== 0n) {
      obj.parentSlot = message.parentSlot.toString();
    }
    if (message.parentBlockhash !== "") {
      obj.parentBlockhash = message.parentBlockhash;
    }
    if (message.executedTransactionCount !== 0n) {
      obj.executedTransactionCount = message.executedTransactionCount.toString();
    }
    if (message.transactions?.length) {
      obj.transactions = message.transactions.map((e) => SubscribeUpdateTransactionInfo.toJSON(e));
    }
    if (message.updatedAccountCount !== 0n) {
      obj.updatedAccountCount = message.updatedAccountCount.toString();
    }
    if (message.accounts?.length) {
      obj.accounts = message.accounts.map((e) => SubscribeUpdateAccountInfo.toJSON(e));
    }
    if (message.entriesCount !== 0n) {
      obj.entriesCount = message.entriesCount.toString();
    }
    if (message.entries?.length) {
      obj.entries = message.entries.map((e) => SubscribeUpdateEntry.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateBlock>, I>>(base?: I): SubscribeUpdateBlock {
    return SubscribeUpdateBlock.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateBlock>, I>>(object: I): SubscribeUpdateBlock {
    const message = createBaseSubscribeUpdateBlock();
    message.slot = object.slot ?? 0n;
    message.blockhash = object.blockhash ?? "";
    message.rewards = (object.rewards !== undefined && object.rewards !== null)
      ? Rewards.fromPartial(object.rewards)
      : undefined;
    message.blockTime = (object.blockTime !== undefined && object.blockTime !== null)
      ? UnixTimestamp.fromPartial(object.blockTime)
      : undefined;
    message.blockHeight = (object.blockHeight !== undefined && object.blockHeight !== null)
      ? BlockHeight.fromPartial(object.blockHeight)
      : undefined;
    message.parentSlot = object.parentSlot ?? 0n;
    message.parentBlockhash = object.parentBlockhash ?? "";
    message.executedTransactionCount = object.executedTransactionCount ?? 0n;
    message.transactions = object.transactions?.map((e) => SubscribeUpdateTransactionInfo.fromPartial(e)) || [];
    message.updatedAccountCount = object.updatedAccountCount ?? 0n;
    message.accounts = object.accounts?.map((e) => SubscribeUpdateAccountInfo.fromPartial(e)) || [];
    message.entriesCount = object.entriesCount ?? 0n;
    message.entries = object.entries?.map((e) => SubscribeUpdateEntry.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSubscribeUpdateBlockMeta(): SubscribeUpdateBlockMeta {
  return {
    slot: 0n,
    blockhash: "",
    rewards: undefined,
    blockTime: undefined,
    blockHeight: undefined,
    parentSlot: 0n,
    parentBlockhash: "",
    executedTransactionCount: 0n,
    entriesCount: 0n,
  };
}

export const SubscribeUpdateBlockMeta: MessageFns<SubscribeUpdateBlockMeta> = {
  encode(message: SubscribeUpdateBlockMeta, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.blockhash !== "") {
      writer.uint32(18).string(message.blockhash);
    }
    if (message.rewards !== undefined) {
      Rewards.encode(message.rewards, writer.uint32(26).fork()).join();
    }
    if (message.blockTime !== undefined) {
      UnixTimestamp.encode(message.blockTime, writer.uint32(34).fork()).join();
    }
    if (message.blockHeight !== undefined) {
      BlockHeight.encode(message.blockHeight, writer.uint32(42).fork()).join();
    }
    if (message.parentSlot !== 0n) {
      if (BigInt.asUintN(64, message.parentSlot) !== message.parentSlot) {
        throw new globalThis.Error("value provided for field message.parentSlot of type uint64 too large");
      }
      writer.uint32(48).uint64(message.parentSlot);
    }
    if (message.parentBlockhash !== "") {
      writer.uint32(58).string(message.parentBlockhash);
    }
    if (message.executedTransactionCount !== 0n) {
      if (BigInt.asUintN(64, message.executedTransactionCount) !== message.executedTransactionCount) {
        throw new globalThis.Error(
          "value provided for field message.executedTransactionCount of type uint64 too large",
        );
      }
      writer.uint32(64).uint64(message.executedTransactionCount);
    }
    if (message.entriesCount !== 0n) {
      if (BigInt.asUintN(64, message.entriesCount) !== message.entriesCount) {
        throw new globalThis.Error("value provided for field message.entriesCount of type uint64 too large");
      }
      writer.uint32(72).uint64(message.entriesCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateBlockMeta {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateBlockMeta();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.blockhash = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rewards = Rewards.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.blockTime = UnixTimestamp.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.blockHeight = BlockHeight.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.parentSlot = reader.uint64() as bigint;
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.parentBlockhash = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.executedTransactionCount = reader.uint64() as bigint;
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.entriesCount = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateBlockMeta, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateBlockMeta | SubscribeUpdateBlockMeta[]>
      | Iterable<SubscribeUpdateBlockMeta | SubscribeUpdateBlockMeta[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateBlockMeta.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateBlockMeta.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateBlockMeta>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateBlockMeta> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateBlockMeta.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateBlockMeta.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateBlockMeta {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      blockhash: isSet(object.blockhash) ? globalThis.String(object.blockhash) : "",
      rewards: isSet(object.rewards) ? Rewards.fromJSON(object.rewards) : undefined,
      blockTime: isSet(object.blockTime) ? UnixTimestamp.fromJSON(object.blockTime) : undefined,
      blockHeight: isSet(object.blockHeight) ? BlockHeight.fromJSON(object.blockHeight) : undefined,
      parentSlot: isSet(object.parentSlot) ? BigInt(object.parentSlot) : 0n,
      parentBlockhash: isSet(object.parentBlockhash) ? globalThis.String(object.parentBlockhash) : "",
      executedTransactionCount: isSet(object.executedTransactionCount) ? BigInt(object.executedTransactionCount) : 0n,
      entriesCount: isSet(object.entriesCount) ? BigInt(object.entriesCount) : 0n,
    };
  },

  toJSON(message: SubscribeUpdateBlockMeta): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.blockhash !== "") {
      obj.blockhash = message.blockhash;
    }
    if (message.rewards !== undefined) {
      obj.rewards = Rewards.toJSON(message.rewards);
    }
    if (message.blockTime !== undefined) {
      obj.blockTime = UnixTimestamp.toJSON(message.blockTime);
    }
    if (message.blockHeight !== undefined) {
      obj.blockHeight = BlockHeight.toJSON(message.blockHeight);
    }
    if (message.parentSlot !== 0n) {
      obj.parentSlot = message.parentSlot.toString();
    }
    if (message.parentBlockhash !== "") {
      obj.parentBlockhash = message.parentBlockhash;
    }
    if (message.executedTransactionCount !== 0n) {
      obj.executedTransactionCount = message.executedTransactionCount.toString();
    }
    if (message.entriesCount !== 0n) {
      obj.entriesCount = message.entriesCount.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateBlockMeta>, I>>(base?: I): SubscribeUpdateBlockMeta {
    return SubscribeUpdateBlockMeta.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateBlockMeta>, I>>(object: I): SubscribeUpdateBlockMeta {
    const message = createBaseSubscribeUpdateBlockMeta();
    message.slot = object.slot ?? 0n;
    message.blockhash = object.blockhash ?? "";
    message.rewards = (object.rewards !== undefined && object.rewards !== null)
      ? Rewards.fromPartial(object.rewards)
      : undefined;
    message.blockTime = (object.blockTime !== undefined && object.blockTime !== null)
      ? UnixTimestamp.fromPartial(object.blockTime)
      : undefined;
    message.blockHeight = (object.blockHeight !== undefined && object.blockHeight !== null)
      ? BlockHeight.fromPartial(object.blockHeight)
      : undefined;
    message.parentSlot = object.parentSlot ?? 0n;
    message.parentBlockhash = object.parentBlockhash ?? "";
    message.executedTransactionCount = object.executedTransactionCount ?? 0n;
    message.entriesCount = object.entriesCount ?? 0n;
    return message;
  },
};

function createBaseSubscribeUpdateEntry(): SubscribeUpdateEntry {
  return {
    slot: 0n,
    index: 0n,
    numHashes: 0n,
    hash: Buffer.alloc(0),
    executedTransactionCount: 0n,
    startingTransactionIndex: 0n,
  };
}

export const SubscribeUpdateEntry: MessageFns<SubscribeUpdateEntry> = {
  encode(message: SubscribeUpdateEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.index !== 0n) {
      if (BigInt.asUintN(64, message.index) !== message.index) {
        throw new globalThis.Error("value provided for field message.index of type uint64 too large");
      }
      writer.uint32(16).uint64(message.index);
    }
    if (message.numHashes !== 0n) {
      if (BigInt.asUintN(64, message.numHashes) !== message.numHashes) {
        throw new globalThis.Error("value provided for field message.numHashes of type uint64 too large");
      }
      writer.uint32(24).uint64(message.numHashes);
    }
    if (message.hash.length !== 0) {
      writer.uint32(34).bytes(message.hash);
    }
    if (message.executedTransactionCount !== 0n) {
      if (BigInt.asUintN(64, message.executedTransactionCount) !== message.executedTransactionCount) {
        throw new globalThis.Error(
          "value provided for field message.executedTransactionCount of type uint64 too large",
        );
      }
      writer.uint32(40).uint64(message.executedTransactionCount);
    }
    if (message.startingTransactionIndex !== 0n) {
      if (BigInt.asUintN(64, message.startingTransactionIndex) !== message.startingTransactionIndex) {
        throw new globalThis.Error(
          "value provided for field message.startingTransactionIndex of type uint64 too large",
        );
      }
      writer.uint32(48).uint64(message.startingTransactionIndex);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdateEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdateEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.index = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.numHashes = reader.uint64() as bigint;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.hash = Buffer.from(reader.bytes());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.executedTransactionCount = reader.uint64() as bigint;
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.startingTransactionIndex = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdateEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdateEntry | SubscribeUpdateEntry[]>
      | Iterable<SubscribeUpdateEntry | SubscribeUpdateEntry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateEntry.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdateEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdateEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdateEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdateEntry.decode(p)];
        }
      } else {
        yield* [SubscribeUpdateEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdateEntry {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      index: isSet(object.index) ? BigInt(object.index) : 0n,
      numHashes: isSet(object.numHashes) ? BigInt(object.numHashes) : 0n,
      hash: isSet(object.hash) ? Buffer.from(bytesFromBase64(object.hash)) : Buffer.alloc(0),
      executedTransactionCount: isSet(object.executedTransactionCount) ? BigInt(object.executedTransactionCount) : 0n,
      startingTransactionIndex: isSet(object.startingTransactionIndex) ? BigInt(object.startingTransactionIndex) : 0n,
    };
  },

  toJSON(message: SubscribeUpdateEntry): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.index !== 0n) {
      obj.index = message.index.toString();
    }
    if (message.numHashes !== 0n) {
      obj.numHashes = message.numHashes.toString();
    }
    if (message.hash.length !== 0) {
      obj.hash = base64FromBytes(message.hash);
    }
    if (message.executedTransactionCount !== 0n) {
      obj.executedTransactionCount = message.executedTransactionCount.toString();
    }
    if (message.startingTransactionIndex !== 0n) {
      obj.startingTransactionIndex = message.startingTransactionIndex.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdateEntry>, I>>(base?: I): SubscribeUpdateEntry {
    return SubscribeUpdateEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdateEntry>, I>>(object: I): SubscribeUpdateEntry {
    const message = createBaseSubscribeUpdateEntry();
    message.slot = object.slot ?? 0n;
    message.index = object.index ?? 0n;
    message.numHashes = object.numHashes ?? 0n;
    message.hash = object.hash ?? Buffer.alloc(0);
    message.executedTransactionCount = object.executedTransactionCount ?? 0n;
    message.startingTransactionIndex = object.startingTransactionIndex ?? 0n;
    return message;
  },
};

function createBaseSubscribeUpdatePing(): SubscribeUpdatePing {
  return {};
}

export const SubscribeUpdatePing: MessageFns<SubscribeUpdatePing> = {
  encode(_: SubscribeUpdatePing, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdatePing {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdatePing();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdatePing, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdatePing | SubscribeUpdatePing[]>
      | Iterable<SubscribeUpdatePing | SubscribeUpdatePing[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdatePing.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdatePing.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdatePing>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdatePing> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdatePing.decode(p)];
        }
      } else {
        yield* [SubscribeUpdatePing.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): SubscribeUpdatePing {
    return {};
  },

  toJSON(_: SubscribeUpdatePing): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdatePing>, I>>(base?: I): SubscribeUpdatePing {
    return SubscribeUpdatePing.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdatePing>, I>>(_: I): SubscribeUpdatePing {
    const message = createBaseSubscribeUpdatePing();
    return message;
  },
};

function createBaseSubscribeUpdatePong(): SubscribeUpdatePong {
  return { id: 0 };
}

export const SubscribeUpdatePong: MessageFns<SubscribeUpdatePong> = {
  encode(message: SubscribeUpdatePong, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int32(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeUpdatePong {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeUpdatePong();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeUpdatePong, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeUpdatePong | SubscribeUpdatePong[]>
      | Iterable<SubscribeUpdatePong | SubscribeUpdatePong[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdatePong.encode(p).finish()];
        }
      } else {
        yield* [SubscribeUpdatePong.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeUpdatePong>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeUpdatePong> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeUpdatePong.decode(p)];
        }
      } else {
        yield* [SubscribeUpdatePong.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeUpdatePong {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  toJSON(message: SubscribeUpdatePong): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeUpdatePong>, I>>(base?: I): SubscribeUpdatePong {
    return SubscribeUpdatePong.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeUpdatePong>, I>>(object: I): SubscribeUpdatePong {
    const message = createBaseSubscribeUpdatePong();
    message.id = object.id ?? 0;
    return message;
  },
};

function createBaseSubscribeReplayInfoRequest(): SubscribeReplayInfoRequest {
  return {};
}

export const SubscribeReplayInfoRequest: MessageFns<SubscribeReplayInfoRequest> = {
  encode(_: SubscribeReplayInfoRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeReplayInfoRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeReplayInfoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeReplayInfoRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeReplayInfoRequest | SubscribeReplayInfoRequest[]>
      | Iterable<SubscribeReplayInfoRequest | SubscribeReplayInfoRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeReplayInfoRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribeReplayInfoRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeReplayInfoRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeReplayInfoRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeReplayInfoRequest.decode(p)];
        }
      } else {
        yield* [SubscribeReplayInfoRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): SubscribeReplayInfoRequest {
    return {};
  },

  toJSON(_: SubscribeReplayInfoRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeReplayInfoRequest>, I>>(base?: I): SubscribeReplayInfoRequest {
    return SubscribeReplayInfoRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeReplayInfoRequest>, I>>(_: I): SubscribeReplayInfoRequest {
    const message = createBaseSubscribeReplayInfoRequest();
    return message;
  },
};

function createBaseSubscribeReplayInfoResponse(): SubscribeReplayInfoResponse {
  return { firstAvailable: undefined };
}

export const SubscribeReplayInfoResponse: MessageFns<SubscribeReplayInfoResponse> = {
  encode(message: SubscribeReplayInfoResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.firstAvailable !== undefined) {
      if (BigInt.asUintN(64, message.firstAvailable) !== message.firstAvailable) {
        throw new globalThis.Error("value provided for field message.firstAvailable of type uint64 too large");
      }
      writer.uint32(8).uint64(message.firstAvailable);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeReplayInfoResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeReplayInfoResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.firstAvailable = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeReplayInfoResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeReplayInfoResponse | SubscribeReplayInfoResponse[]>
      | Iterable<SubscribeReplayInfoResponse | SubscribeReplayInfoResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeReplayInfoResponse.encode(p).finish()];
        }
      } else {
        yield* [SubscribeReplayInfoResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeReplayInfoResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeReplayInfoResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeReplayInfoResponse.decode(p)];
        }
      } else {
        yield* [SubscribeReplayInfoResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeReplayInfoResponse {
    return { firstAvailable: isSet(object.firstAvailable) ? BigInt(object.firstAvailable) : undefined };
  },

  toJSON(message: SubscribeReplayInfoResponse): unknown {
    const obj: any = {};
    if (message.firstAvailable !== undefined) {
      obj.firstAvailable = message.firstAvailable.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeReplayInfoResponse>, I>>(base?: I): SubscribeReplayInfoResponse {
    return SubscribeReplayInfoResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeReplayInfoResponse>, I>>(object: I): SubscribeReplayInfoResponse {
    const message = createBaseSubscribeReplayInfoResponse();
    message.firstAvailable = object.firstAvailable ?? undefined;
    return message;
  },
};

function createBasePingRequest(): PingRequest {
  return { count: 0 };
}

export const PingRequest: MessageFns<PingRequest> = {
  encode(message: PingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.count !== 0) {
      writer.uint32(8).int32(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<PingRequest, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<PingRequest | PingRequest[]> | Iterable<PingRequest | PingRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PingRequest.encode(p).finish()];
        }
      } else {
        yield* [PingRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, PingRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<PingRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PingRequest.decode(p)];
        }
      } else {
        yield* [PingRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): PingRequest {
    return { count: isSet(object.count) ? globalThis.Number(object.count) : 0 };
  },

  toJSON(message: PingRequest): unknown {
    const obj: any = {};
    if (message.count !== 0) {
      obj.count = Math.round(message.count);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PingRequest>, I>>(base?: I): PingRequest {
    return PingRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PingRequest>, I>>(object: I): PingRequest {
    const message = createBasePingRequest();
    message.count = object.count ?? 0;
    return message;
  },
};

function createBasePongResponse(): PongResponse {
  return { count: 0 };
}

export const PongResponse: MessageFns<PongResponse> = {
  encode(message: PongResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.count !== 0) {
      writer.uint32(8).int32(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PongResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePongResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<PongResponse, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<PongResponse | PongResponse[]> | Iterable<PongResponse | PongResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PongResponse.encode(p).finish()];
        }
      } else {
        yield* [PongResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, PongResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<PongResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PongResponse.decode(p)];
        }
      } else {
        yield* [PongResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): PongResponse {
    return { count: isSet(object.count) ? globalThis.Number(object.count) : 0 };
  },

  toJSON(message: PongResponse): unknown {
    const obj: any = {};
    if (message.count !== 0) {
      obj.count = Math.round(message.count);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PongResponse>, I>>(base?: I): PongResponse {
    return PongResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PongResponse>, I>>(object: I): PongResponse {
    const message = createBasePongResponse();
    message.count = object.count ?? 0;
    return message;
  },
};

function createBaseGetLatestBlockhashRequest(): GetLatestBlockhashRequest {
  return { commitment: undefined };
}

export const GetLatestBlockhashRequest: MessageFns<GetLatestBlockhashRequest> = {
  encode(message: GetLatestBlockhashRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.commitment !== undefined) {
      writer.uint32(8).int32(message.commitment);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetLatestBlockhashRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetLatestBlockhashRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.commitment = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetLatestBlockhashRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GetLatestBlockhashRequest | GetLatestBlockhashRequest[]>
      | Iterable<GetLatestBlockhashRequest | GetLatestBlockhashRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetLatestBlockhashRequest.encode(p).finish()];
        }
      } else {
        yield* [GetLatestBlockhashRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetLatestBlockhashRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetLatestBlockhashRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetLatestBlockhashRequest.decode(p)];
        }
      } else {
        yield* [GetLatestBlockhashRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GetLatestBlockhashRequest {
    return { commitment: isSet(object.commitment) ? commitmentLevelFromJSON(object.commitment) : undefined };
  },

  toJSON(message: GetLatestBlockhashRequest): unknown {
    const obj: any = {};
    if (message.commitment !== undefined) {
      obj.commitment = commitmentLevelToJSON(message.commitment);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetLatestBlockhashRequest>, I>>(base?: I): GetLatestBlockhashRequest {
    return GetLatestBlockhashRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLatestBlockhashRequest>, I>>(object: I): GetLatestBlockhashRequest {
    const message = createBaseGetLatestBlockhashRequest();
    message.commitment = object.commitment ?? undefined;
    return message;
  },
};

function createBaseGetLatestBlockhashResponse(): GetLatestBlockhashResponse {
  return { slot: 0n, blockhash: "", lastValidBlockHeight: 0n };
}

export const GetLatestBlockhashResponse: MessageFns<GetLatestBlockhashResponse> = {
  encode(message: GetLatestBlockhashResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.blockhash !== "") {
      writer.uint32(18).string(message.blockhash);
    }
    if (message.lastValidBlockHeight !== 0n) {
      if (BigInt.asUintN(64, message.lastValidBlockHeight) !== message.lastValidBlockHeight) {
        throw new globalThis.Error("value provided for field message.lastValidBlockHeight of type uint64 too large");
      }
      writer.uint32(24).uint64(message.lastValidBlockHeight);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetLatestBlockhashResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetLatestBlockhashResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.blockhash = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.lastValidBlockHeight = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetLatestBlockhashResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GetLatestBlockhashResponse | GetLatestBlockhashResponse[]>
      | Iterable<GetLatestBlockhashResponse | GetLatestBlockhashResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetLatestBlockhashResponse.encode(p).finish()];
        }
      } else {
        yield* [GetLatestBlockhashResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetLatestBlockhashResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetLatestBlockhashResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetLatestBlockhashResponse.decode(p)];
        }
      } else {
        yield* [GetLatestBlockhashResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GetLatestBlockhashResponse {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      blockhash: isSet(object.blockhash) ? globalThis.String(object.blockhash) : "",
      lastValidBlockHeight: isSet(object.lastValidBlockHeight) ? BigInt(object.lastValidBlockHeight) : 0n,
    };
  },

  toJSON(message: GetLatestBlockhashResponse): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.blockhash !== "") {
      obj.blockhash = message.blockhash;
    }
    if (message.lastValidBlockHeight !== 0n) {
      obj.lastValidBlockHeight = message.lastValidBlockHeight.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetLatestBlockhashResponse>, I>>(base?: I): GetLatestBlockhashResponse {
    return GetLatestBlockhashResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLatestBlockhashResponse>, I>>(object: I): GetLatestBlockhashResponse {
    const message = createBaseGetLatestBlockhashResponse();
    message.slot = object.slot ?? 0n;
    message.blockhash = object.blockhash ?? "";
    message.lastValidBlockHeight = object.lastValidBlockHeight ?? 0n;
    return message;
  },
};

function createBaseGetBlockHeightRequest(): GetBlockHeightRequest {
  return { commitment: undefined };
}

export const GetBlockHeightRequest: MessageFns<GetBlockHeightRequest> = {
  encode(message: GetBlockHeightRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.commitment !== undefined) {
      writer.uint32(8).int32(message.commitment);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetBlockHeightRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBlockHeightRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.commitment = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetBlockHeightRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GetBlockHeightRequest | GetBlockHeightRequest[]>
      | Iterable<GetBlockHeightRequest | GetBlockHeightRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetBlockHeightRequest.encode(p).finish()];
        }
      } else {
        yield* [GetBlockHeightRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetBlockHeightRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetBlockHeightRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetBlockHeightRequest.decode(p)];
        }
      } else {
        yield* [GetBlockHeightRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GetBlockHeightRequest {
    return { commitment: isSet(object.commitment) ? commitmentLevelFromJSON(object.commitment) : undefined };
  },

  toJSON(message: GetBlockHeightRequest): unknown {
    const obj: any = {};
    if (message.commitment !== undefined) {
      obj.commitment = commitmentLevelToJSON(message.commitment);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBlockHeightRequest>, I>>(base?: I): GetBlockHeightRequest {
    return GetBlockHeightRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBlockHeightRequest>, I>>(object: I): GetBlockHeightRequest {
    const message = createBaseGetBlockHeightRequest();
    message.commitment = object.commitment ?? undefined;
    return message;
  },
};

function createBaseGetBlockHeightResponse(): GetBlockHeightResponse {
  return { blockHeight: 0n };
}

export const GetBlockHeightResponse: MessageFns<GetBlockHeightResponse> = {
  encode(message: GetBlockHeightResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.blockHeight !== 0n) {
      if (BigInt.asUintN(64, message.blockHeight) !== message.blockHeight) {
        throw new globalThis.Error("value provided for field message.blockHeight of type uint64 too large");
      }
      writer.uint32(8).uint64(message.blockHeight);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetBlockHeightResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBlockHeightResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.blockHeight = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetBlockHeightResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GetBlockHeightResponse | GetBlockHeightResponse[]>
      | Iterable<GetBlockHeightResponse | GetBlockHeightResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetBlockHeightResponse.encode(p).finish()];
        }
      } else {
        yield* [GetBlockHeightResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetBlockHeightResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetBlockHeightResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetBlockHeightResponse.decode(p)];
        }
      } else {
        yield* [GetBlockHeightResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GetBlockHeightResponse {
    return { blockHeight: isSet(object.blockHeight) ? BigInt(object.blockHeight) : 0n };
  },

  toJSON(message: GetBlockHeightResponse): unknown {
    const obj: any = {};
    if (message.blockHeight !== 0n) {
      obj.blockHeight = message.blockHeight.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBlockHeightResponse>, I>>(base?: I): GetBlockHeightResponse {
    return GetBlockHeightResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBlockHeightResponse>, I>>(object: I): GetBlockHeightResponse {
    const message = createBaseGetBlockHeightResponse();
    message.blockHeight = object.blockHeight ?? 0n;
    return message;
  },
};

function createBaseGetSlotRequest(): GetSlotRequest {
  return { commitment: undefined };
}

export const GetSlotRequest: MessageFns<GetSlotRequest> = {
  encode(message: GetSlotRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.commitment !== undefined) {
      writer.uint32(8).int32(message.commitment);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetSlotRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetSlotRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.commitment = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetSlotRequest, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<GetSlotRequest | GetSlotRequest[]> | Iterable<GetSlotRequest | GetSlotRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetSlotRequest.encode(p).finish()];
        }
      } else {
        yield* [GetSlotRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetSlotRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetSlotRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetSlotRequest.decode(p)];
        }
      } else {
        yield* [GetSlotRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GetSlotRequest {
    return { commitment: isSet(object.commitment) ? commitmentLevelFromJSON(object.commitment) : undefined };
  },

  toJSON(message: GetSlotRequest): unknown {
    const obj: any = {};
    if (message.commitment !== undefined) {
      obj.commitment = commitmentLevelToJSON(message.commitment);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetSlotRequest>, I>>(base?: I): GetSlotRequest {
    return GetSlotRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSlotRequest>, I>>(object: I): GetSlotRequest {
    const message = createBaseGetSlotRequest();
    message.commitment = object.commitment ?? undefined;
    return message;
  },
};

function createBaseGetSlotResponse(): GetSlotResponse {
  return { slot: 0n };
}

export const GetSlotResponse: MessageFns<GetSlotResponse> = {
  encode(message: GetSlotResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetSlotResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetSlotResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetSlotResponse, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<GetSlotResponse | GetSlotResponse[]> | Iterable<GetSlotResponse | GetSlotResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetSlotResponse.encode(p).finish()];
        }
      } else {
        yield* [GetSlotResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetSlotResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetSlotResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetSlotResponse.decode(p)];
        }
      } else {
        yield* [GetSlotResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GetSlotResponse {
    return { slot: isSet(object.slot) ? BigInt(object.slot) : 0n };
  },

  toJSON(message: GetSlotResponse): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetSlotResponse>, I>>(base?: I): GetSlotResponse {
    return GetSlotResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSlotResponse>, I>>(object: I): GetSlotResponse {
    const message = createBaseGetSlotResponse();
    message.slot = object.slot ?? 0n;
    return message;
  },
};

function createBaseGetVersionRequest(): GetVersionRequest {
  return {};
}

export const GetVersionRequest: MessageFns<GetVersionRequest> = {
  encode(_: GetVersionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetVersionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetVersionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetVersionRequest, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<GetVersionRequest | GetVersionRequest[]> | Iterable<GetVersionRequest | GetVersionRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetVersionRequest.encode(p).finish()];
        }
      } else {
        yield* [GetVersionRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetVersionRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetVersionRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetVersionRequest.decode(p)];
        }
      } else {
        yield* [GetVersionRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): GetVersionRequest {
    return {};
  },

  toJSON(_: GetVersionRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GetVersionRequest>, I>>(base?: I): GetVersionRequest {
    return GetVersionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVersionRequest>, I>>(_: I): GetVersionRequest {
    const message = createBaseGetVersionRequest();
    return message;
  },
};

function createBaseGetVersionResponse(): GetVersionResponse {
  return { version: "" };
}

export const GetVersionResponse: MessageFns<GetVersionResponse> = {
  encode(message: GetVersionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.version !== "") {
      writer.uint32(10).string(message.version);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetVersionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetVersionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.version = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetVersionResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GetVersionResponse | GetVersionResponse[]>
      | Iterable<GetVersionResponse | GetVersionResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetVersionResponse.encode(p).finish()];
        }
      } else {
        yield* [GetVersionResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetVersionResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetVersionResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetVersionResponse.decode(p)];
        }
      } else {
        yield* [GetVersionResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GetVersionResponse {
    return { version: isSet(object.version) ? globalThis.String(object.version) : "" };
  },

  toJSON(message: GetVersionResponse): unknown {
    const obj: any = {};
    if (message.version !== "") {
      obj.version = message.version;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetVersionResponse>, I>>(base?: I): GetVersionResponse {
    return GetVersionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVersionResponse>, I>>(object: I): GetVersionResponse {
    const message = createBaseGetVersionResponse();
    message.version = object.version ?? "";
    return message;
  },
};

function createBaseIsBlockhashValidRequest(): IsBlockhashValidRequest {
  return { blockhash: "", commitment: undefined };
}

export const IsBlockhashValidRequest: MessageFns<IsBlockhashValidRequest> = {
  encode(message: IsBlockhashValidRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.blockhash !== "") {
      writer.uint32(10).string(message.blockhash);
    }
    if (message.commitment !== undefined) {
      writer.uint32(16).int32(message.commitment);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IsBlockhashValidRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIsBlockhashValidRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.blockhash = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.commitment = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<IsBlockhashValidRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<IsBlockhashValidRequest | IsBlockhashValidRequest[]>
      | Iterable<IsBlockhashValidRequest | IsBlockhashValidRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [IsBlockhashValidRequest.encode(p).finish()];
        }
      } else {
        yield* [IsBlockhashValidRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, IsBlockhashValidRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<IsBlockhashValidRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [IsBlockhashValidRequest.decode(p)];
        }
      } else {
        yield* [IsBlockhashValidRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): IsBlockhashValidRequest {
    return {
      blockhash: isSet(object.blockhash) ? globalThis.String(object.blockhash) : "",
      commitment: isSet(object.commitment) ? commitmentLevelFromJSON(object.commitment) : undefined,
    };
  },

  toJSON(message: IsBlockhashValidRequest): unknown {
    const obj: any = {};
    if (message.blockhash !== "") {
      obj.blockhash = message.blockhash;
    }
    if (message.commitment !== undefined) {
      obj.commitment = commitmentLevelToJSON(message.commitment);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IsBlockhashValidRequest>, I>>(base?: I): IsBlockhashValidRequest {
    return IsBlockhashValidRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IsBlockhashValidRequest>, I>>(object: I): IsBlockhashValidRequest {
    const message = createBaseIsBlockhashValidRequest();
    message.blockhash = object.blockhash ?? "";
    message.commitment = object.commitment ?? undefined;
    return message;
  },
};

function createBaseIsBlockhashValidResponse(): IsBlockhashValidResponse {
  return { slot: 0n, valid: false };
}

export const IsBlockhashValidResponse: MessageFns<IsBlockhashValidResponse> = {
  encode(message: IsBlockhashValidResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.valid !== false) {
      writer.uint32(16).bool(message.valid);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IsBlockhashValidResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIsBlockhashValidResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.valid = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<IsBlockhashValidResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<IsBlockhashValidResponse | IsBlockhashValidResponse[]>
      | Iterable<IsBlockhashValidResponse | IsBlockhashValidResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [IsBlockhashValidResponse.encode(p).finish()];
        }
      } else {
        yield* [IsBlockhashValidResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, IsBlockhashValidResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<IsBlockhashValidResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [IsBlockhashValidResponse.decode(p)];
        }
      } else {
        yield* [IsBlockhashValidResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): IsBlockhashValidResponse {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      valid: isSet(object.valid) ? globalThis.Boolean(object.valid) : false,
    };
  },

  toJSON(message: IsBlockhashValidResponse): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.valid !== false) {
      obj.valid = message.valid;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IsBlockhashValidResponse>, I>>(base?: I): IsBlockhashValidResponse {
    return IsBlockhashValidResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IsBlockhashValidResponse>, I>>(object: I): IsBlockhashValidResponse {
    const message = createBaseIsBlockhashValidResponse();
    message.slot = object.slot ?? 0n;
    message.valid = object.valid ?? false;
    return message;
  },
};

export type GeyserService = typeof GeyserService;
export const GeyserService = {
  subscribe: {
    path: "/geyser.Geyser/Subscribe",
    requestStream: true,
    responseStream: true,
    requestSerialize: (value: SubscribeRequest) => Buffer.from(SubscribeRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribeRequest.decode(value),
    responseSerialize: (value: SubscribeUpdate) => Buffer.from(SubscribeUpdate.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SubscribeUpdate.decode(value),
  },
  subscribeReplayInfo: {
    path: "/geyser.Geyser/SubscribeReplayInfo",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: SubscribeReplayInfoRequest) =>
      Buffer.from(SubscribeReplayInfoRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribeReplayInfoRequest.decode(value),
    responseSerialize: (value: SubscribeReplayInfoResponse) =>
      Buffer.from(SubscribeReplayInfoResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SubscribeReplayInfoResponse.decode(value),
  },
  ping: {
    path: "/geyser.Geyser/Ping",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: PingRequest) => Buffer.from(PingRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => PingRequest.decode(value),
    responseSerialize: (value: PongResponse) => Buffer.from(PongResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => PongResponse.decode(value),
  },
  getLatestBlockhash: {
    path: "/geyser.Geyser/GetLatestBlockhash",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetLatestBlockhashRequest) =>
      Buffer.from(GetLatestBlockhashRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetLatestBlockhashRequest.decode(value),
    responseSerialize: (value: GetLatestBlockhashResponse) =>
      Buffer.from(GetLatestBlockhashResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetLatestBlockhashResponse.decode(value),
  },
  getBlockHeight: {
    path: "/geyser.Geyser/GetBlockHeight",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetBlockHeightRequest) => Buffer.from(GetBlockHeightRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetBlockHeightRequest.decode(value),
    responseSerialize: (value: GetBlockHeightResponse) => Buffer.from(GetBlockHeightResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetBlockHeightResponse.decode(value),
  },
  getSlot: {
    path: "/geyser.Geyser/GetSlot",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetSlotRequest) => Buffer.from(GetSlotRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetSlotRequest.decode(value),
    responseSerialize: (value: GetSlotResponse) => Buffer.from(GetSlotResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetSlotResponse.decode(value),
  },
  isBlockhashValid: {
    path: "/geyser.Geyser/IsBlockhashValid",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: IsBlockhashValidRequest) => Buffer.from(IsBlockhashValidRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => IsBlockhashValidRequest.decode(value),
    responseSerialize: (value: IsBlockhashValidResponse) =>
      Buffer.from(IsBlockhashValidResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => IsBlockhashValidResponse.decode(value),
  },
  getVersion: {
    path: "/geyser.Geyser/GetVersion",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetVersionRequest) => Buffer.from(GetVersionRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetVersionRequest.decode(value),
    responseSerialize: (value: GetVersionResponse) => Buffer.from(GetVersionResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetVersionResponse.decode(value),
  },
} as const;

export interface GeyserServer extends UntypedServiceImplementation {
  subscribe: handleBidiStreamingCall<SubscribeRequest, SubscribeUpdate>;
  subscribeReplayInfo: handleUnaryCall<SubscribeReplayInfoRequest, SubscribeReplayInfoResponse>;
  ping: handleUnaryCall<PingRequest, PongResponse>;
  getLatestBlockhash: handleUnaryCall<GetLatestBlockhashRequest, GetLatestBlockhashResponse>;
  getBlockHeight: handleUnaryCall<GetBlockHeightRequest, GetBlockHeightResponse>;
  getSlot: handleUnaryCall<GetSlotRequest, GetSlotResponse>;
  isBlockhashValid: handleUnaryCall<IsBlockhashValidRequest, IsBlockhashValidResponse>;
  getVersion: handleUnaryCall<GetVersionRequest, GetVersionResponse>;
}

export interface GeyserClient extends Client {
  subscribe(): ClientDuplexStream<SubscribeRequest, SubscribeUpdate>;
  subscribe(options: Partial<CallOptions>): ClientDuplexStream<SubscribeRequest, SubscribeUpdate>;
  subscribe(metadata: Metadata, options?: Partial<CallOptions>): ClientDuplexStream<SubscribeRequest, SubscribeUpdate>;
  subscribeReplayInfo(
    request: SubscribeReplayInfoRequest,
    callback: (error: ServiceError | null, response: SubscribeReplayInfoResponse) => void,
  ): ClientUnaryCall;
  subscribeReplayInfo(
    request: SubscribeReplayInfoRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: SubscribeReplayInfoResponse) => void,
  ): ClientUnaryCall;
  subscribeReplayInfo(
    request: SubscribeReplayInfoRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: SubscribeReplayInfoResponse) => void,
  ): ClientUnaryCall;
  ping(request: PingRequest, callback: (error: ServiceError | null, response: PongResponse) => void): ClientUnaryCall;
  ping(
    request: PingRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: PongResponse) => void,
  ): ClientUnaryCall;
  ping(
    request: PingRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: PongResponse) => void,
  ): ClientUnaryCall;
  getLatestBlockhash(
    request: GetLatestBlockhashRequest,
    callback: (error: ServiceError | null, response: GetLatestBlockhashResponse) => void,
  ): ClientUnaryCall;
  getLatestBlockhash(
    request: GetLatestBlockhashRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetLatestBlockhashResponse) => void,
  ): ClientUnaryCall;
  getLatestBlockhash(
    request: GetLatestBlockhashRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetLatestBlockhashResponse) => void,
  ): ClientUnaryCall;
  getBlockHeight(
    request: GetBlockHeightRequest,
    callback: (error: ServiceError | null, response: GetBlockHeightResponse) => void,
  ): ClientUnaryCall;
  getBlockHeight(
    request: GetBlockHeightRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetBlockHeightResponse) => void,
  ): ClientUnaryCall;
  getBlockHeight(
    request: GetBlockHeightRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetBlockHeightResponse) => void,
  ): ClientUnaryCall;
  getSlot(
    request: GetSlotRequest,
    callback: (error: ServiceError | null, response: GetSlotResponse) => void,
  ): ClientUnaryCall;
  getSlot(
    request: GetSlotRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetSlotResponse) => void,
  ): ClientUnaryCall;
  getSlot(
    request: GetSlotRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetSlotResponse) => void,
  ): ClientUnaryCall;
  isBlockhashValid(
    request: IsBlockhashValidRequest,
    callback: (error: ServiceError | null, response: IsBlockhashValidResponse) => void,
  ): ClientUnaryCall;
  isBlockhashValid(
    request: IsBlockhashValidRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: IsBlockhashValidResponse) => void,
  ): ClientUnaryCall;
  isBlockhashValid(
    request: IsBlockhashValidRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: IsBlockhashValidResponse) => void,
  ): ClientUnaryCall;
  getVersion(
    request: GetVersionRequest,
    callback: (error: ServiceError | null, response: GetVersionResponse) => void,
  ): ClientUnaryCall;
  getVersion(
    request: GetVersionRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetVersionResponse) => void,
  ): ClientUnaryCall;
  getVersion(
    request: GetVersionRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetVersionResponse) => void,
  ): ClientUnaryCall;
}

export const GeyserClient = makeGenericClientConstructor(GeyserService, "geyser.Geyser") as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): GeyserClient;
  service: typeof GeyserService;
  serviceName: string;
};

function bytesFromBase64(b64: string): Uint8Array {
  return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}

function base64FromBytes(arr: Uint8Array): string {
  return globalThis.Buffer.from(arr).toString("base64");
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function toTimestamp(date: Date): Timestamp {
  const seconds = BigInt(Math.trunc(date.getTime() / 1_000));
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds.toString()) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

function fromJsonTimestamp(o: any): Date {
  if (o instanceof globalThis.Date) {
    return o;
  } else if (typeof o === "string") {
    return new globalThis.Date(o);
  } else {
    return fromTimestamp(Timestamp.fromJSON(o));
  }
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
