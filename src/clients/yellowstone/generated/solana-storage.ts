// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v5.29.3
// source: solana-storage.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export enum RewardType {
  Unspecified = 0,
  Fee = 1,
  Rent = 2,
  Staking = 3,
  Voting = 4,
  UNRECOGNIZED = -1,
}

export function rewardTypeFromJSON(object: any): RewardType {
  switch (object) {
    case 0:
    case "Unspecified":
      return RewardType.Unspecified;
    case 1:
    case "Fee":
      return RewardType.Fee;
    case 2:
    case "Rent":
      return RewardType.Rent;
    case 3:
    case "Staking":
      return RewardType.Staking;
    case 4:
    case "Voting":
      return RewardType.Voting;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RewardType.UNRECOGNIZED;
  }
}

export function rewardTypeToJSON(object: RewardType): string {
  switch (object) {
    case RewardType.Unspecified:
      return "Unspecified";
    case RewardType.Fee:
      return "Fee";
    case RewardType.Rent:
      return "Rent";
    case RewardType.Staking:
      return "Staking";
    case RewardType.Voting:
      return "Voting";
    case RewardType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface ConfirmedBlock {
  previousBlockhash: string;
  blockhash: string;
  parentSlot: bigint;
  transactions: ConfirmedTransaction[];
  rewards: Reward[];
  blockTime: UnixTimestamp | undefined;
  blockHeight: BlockHeight | undefined;
  numPartitions: NumPartitions | undefined;
}

export interface ConfirmedTransaction {
  transaction: Transaction | undefined;
  meta: TransactionStatusMeta | undefined;
}

export interface Transaction {
  signatures: Buffer[];
  message: Message | undefined;
}

export interface Message {
  header: MessageHeader | undefined;
  accountKeys: Buffer[];
  recentBlockhash: Buffer;
  instructions: CompiledInstruction[];
  versioned: boolean;
  addressTableLookups: MessageAddressTableLookup[];
}

export interface MessageHeader {
  numRequiredSignatures: number;
  numReadonlySignedAccounts: number;
  numReadonlyUnsignedAccounts: number;
}

export interface MessageAddressTableLookup {
  accountKey: Buffer;
  writableIndexes: Buffer;
  readonlyIndexes: Buffer;
}

export interface TransactionStatusMeta {
  err: TransactionError | undefined;
  fee: bigint;
  preBalances: bigint[];
  postBalances: bigint[];
  innerInstructions: InnerInstructions[];
  innerInstructionsNone: boolean;
  logMessages: string[];
  logMessagesNone: boolean;
  preTokenBalances: TokenBalance[];
  postTokenBalances: TokenBalance[];
  rewards: Reward[];
  loadedWritableAddresses: Buffer[];
  loadedReadonlyAddresses: Buffer[];
  returnData: ReturnData | undefined;
  returnDataNone: boolean;
  /**
   * Sum of compute units consumed by all instructions.
   * Available since Solana v1.10.35 / v1.11.6.
   * Set to `None` for txs executed on earlier versions.
   */
  computeUnitsConsumed?: bigint | undefined;
}

export interface TransactionError {
  err: Buffer;
}

export interface InnerInstructions {
  index: number;
  instructions: InnerInstruction[];
}

export interface InnerInstruction {
  programIdIndex: number;
  accounts: Buffer;
  data: Buffer;
  /**
   * Invocation stack height of an inner instruction.
   * Available since Solana v1.14.6
   * Set to `None` for txs executed on earlier versions.
   */
  stackHeight?: number | undefined;
}

export interface CompiledInstruction {
  programIdIndex: number;
  accounts: Buffer;
  data: Buffer;
}

export interface TokenBalance {
  accountIndex: number;
  mint: string;
  uiTokenAmount: UiTokenAmount | undefined;
  owner: string;
  programId: string;
}

export interface UiTokenAmount {
  uiAmount: number;
  decimals: number;
  amount: string;
  uiAmountString: string;
}

export interface ReturnData {
  programId: Buffer;
  data: Buffer;
}

export interface Reward {
  pubkey: string;
  lamports: bigint;
  postBalance: bigint;
  rewardType: RewardType;
  commission: string;
}

export interface Rewards {
  rewards: Reward[];
  numPartitions: NumPartitions | undefined;
}

export interface UnixTimestamp {
  timestamp: bigint;
}

export interface BlockHeight {
  blockHeight: bigint;
}

export interface NumPartitions {
  numPartitions: bigint;
}

function createBaseConfirmedBlock(): ConfirmedBlock {
  return {
    previousBlockhash: "",
    blockhash: "",
    parentSlot: 0n,
    transactions: [],
    rewards: [],
    blockTime: undefined,
    blockHeight: undefined,
    numPartitions: undefined,
  };
}

export const ConfirmedBlock: MessageFns<ConfirmedBlock> = {
  encode(message: ConfirmedBlock, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.previousBlockhash !== "") {
      writer.uint32(10).string(message.previousBlockhash);
    }
    if (message.blockhash !== "") {
      writer.uint32(18).string(message.blockhash);
    }
    if (message.parentSlot !== 0n) {
      if (BigInt.asUintN(64, message.parentSlot) !== message.parentSlot) {
        throw new globalThis.Error("value provided for field message.parentSlot of type uint64 too large");
      }
      writer.uint32(24).uint64(message.parentSlot);
    }
    for (const v of message.transactions) {
      ConfirmedTransaction.encode(v!, writer.uint32(34).fork()).join();
    }
    for (const v of message.rewards) {
      Reward.encode(v!, writer.uint32(42).fork()).join();
    }
    if (message.blockTime !== undefined) {
      UnixTimestamp.encode(message.blockTime, writer.uint32(50).fork()).join();
    }
    if (message.blockHeight !== undefined) {
      BlockHeight.encode(message.blockHeight, writer.uint32(58).fork()).join();
    }
    if (message.numPartitions !== undefined) {
      NumPartitions.encode(message.numPartitions, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConfirmedBlock {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfirmedBlock();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.previousBlockhash = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.blockhash = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.parentSlot = reader.uint64() as bigint;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.transactions.push(ConfirmedTransaction.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.rewards.push(Reward.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.blockTime = UnixTimestamp.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.blockHeight = BlockHeight.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.numPartitions = NumPartitions.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<ConfirmedBlock, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<ConfirmedBlock | ConfirmedBlock[]> | Iterable<ConfirmedBlock | ConfirmedBlock[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConfirmedBlock.encode(p).finish()];
        }
      } else {
        yield* [ConfirmedBlock.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, ConfirmedBlock>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<ConfirmedBlock> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConfirmedBlock.decode(p)];
        }
      } else {
        yield* [ConfirmedBlock.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): ConfirmedBlock {
    return {
      previousBlockhash: isSet(object.previousBlockhash) ? globalThis.String(object.previousBlockhash) : "",
      blockhash: isSet(object.blockhash) ? globalThis.String(object.blockhash) : "",
      parentSlot: isSet(object.parentSlot) ? BigInt(object.parentSlot) : 0n,
      transactions: globalThis.Array.isArray(object?.transactions)
        ? object.transactions.map((e: any) => ConfirmedTransaction.fromJSON(e))
        : [],
      rewards: globalThis.Array.isArray(object?.rewards) ? object.rewards.map((e: any) => Reward.fromJSON(e)) : [],
      blockTime: isSet(object.blockTime) ? UnixTimestamp.fromJSON(object.blockTime) : undefined,
      blockHeight: isSet(object.blockHeight) ? BlockHeight.fromJSON(object.blockHeight) : undefined,
      numPartitions: isSet(object.numPartitions) ? NumPartitions.fromJSON(object.numPartitions) : undefined,
    };
  },

  toJSON(message: ConfirmedBlock): unknown {
    const obj: any = {};
    if (message.previousBlockhash !== "") {
      obj.previousBlockhash = message.previousBlockhash;
    }
    if (message.blockhash !== "") {
      obj.blockhash = message.blockhash;
    }
    if (message.parentSlot !== 0n) {
      obj.parentSlot = message.parentSlot.toString();
    }
    if (message.transactions?.length) {
      obj.transactions = message.transactions.map((e) => ConfirmedTransaction.toJSON(e));
    }
    if (message.rewards?.length) {
      obj.rewards = message.rewards.map((e) => Reward.toJSON(e));
    }
    if (message.blockTime !== undefined) {
      obj.blockTime = UnixTimestamp.toJSON(message.blockTime);
    }
    if (message.blockHeight !== undefined) {
      obj.blockHeight = BlockHeight.toJSON(message.blockHeight);
    }
    if (message.numPartitions !== undefined) {
      obj.numPartitions = NumPartitions.toJSON(message.numPartitions);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConfirmedBlock>, I>>(base?: I): ConfirmedBlock {
    return ConfirmedBlock.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfirmedBlock>, I>>(object: I): ConfirmedBlock {
    const message = createBaseConfirmedBlock();
    message.previousBlockhash = object.previousBlockhash ?? "";
    message.blockhash = object.blockhash ?? "";
    message.parentSlot = object.parentSlot ?? 0n;
    message.transactions = object.transactions?.map((e) => ConfirmedTransaction.fromPartial(e)) || [];
    message.rewards = object.rewards?.map((e) => Reward.fromPartial(e)) || [];
    message.blockTime = (object.blockTime !== undefined && object.blockTime !== null)
      ? UnixTimestamp.fromPartial(object.blockTime)
      : undefined;
    message.blockHeight = (object.blockHeight !== undefined && object.blockHeight !== null)
      ? BlockHeight.fromPartial(object.blockHeight)
      : undefined;
    message.numPartitions = (object.numPartitions !== undefined && object.numPartitions !== null)
      ? NumPartitions.fromPartial(object.numPartitions)
      : undefined;
    return message;
  },
};

function createBaseConfirmedTransaction(): ConfirmedTransaction {
  return { transaction: undefined, meta: undefined };
}

export const ConfirmedTransaction: MessageFns<ConfirmedTransaction> = {
  encode(message: ConfirmedTransaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.transaction !== undefined) {
      Transaction.encode(message.transaction, writer.uint32(10).fork()).join();
    }
    if (message.meta !== undefined) {
      TransactionStatusMeta.encode(message.meta, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConfirmedTransaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfirmedTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transaction = Transaction.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.meta = TransactionStatusMeta.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<ConfirmedTransaction, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<ConfirmedTransaction | ConfirmedTransaction[]>
      | Iterable<ConfirmedTransaction | ConfirmedTransaction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConfirmedTransaction.encode(p).finish()];
        }
      } else {
        yield* [ConfirmedTransaction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, ConfirmedTransaction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<ConfirmedTransaction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConfirmedTransaction.decode(p)];
        }
      } else {
        yield* [ConfirmedTransaction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): ConfirmedTransaction {
    return {
      transaction: isSet(object.transaction) ? Transaction.fromJSON(object.transaction) : undefined,
      meta: isSet(object.meta) ? TransactionStatusMeta.fromJSON(object.meta) : undefined,
    };
  },

  toJSON(message: ConfirmedTransaction): unknown {
    const obj: any = {};
    if (message.transaction !== undefined) {
      obj.transaction = Transaction.toJSON(message.transaction);
    }
    if (message.meta !== undefined) {
      obj.meta = TransactionStatusMeta.toJSON(message.meta);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConfirmedTransaction>, I>>(base?: I): ConfirmedTransaction {
    return ConfirmedTransaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfirmedTransaction>, I>>(object: I): ConfirmedTransaction {
    const message = createBaseConfirmedTransaction();
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? Transaction.fromPartial(object.transaction)
      : undefined;
    message.meta = (object.meta !== undefined && object.meta !== null)
      ? TransactionStatusMeta.fromPartial(object.meta)
      : undefined;
    return message;
  },
};

function createBaseTransaction(): Transaction {
  return { signatures: [], message: undefined };
}

export const Transaction: MessageFns<Transaction> = {
  encode(message: Transaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.signatures) {
      writer.uint32(10).bytes(v!);
    }
    if (message.message !== undefined) {
      Message.encode(message.message, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Transaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.signatures.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = Message.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Transaction, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Transaction | Transaction[]> | Iterable<Transaction | Transaction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Transaction.encode(p).finish()];
        }
      } else {
        yield* [Transaction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Transaction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Transaction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Transaction.decode(p)];
        }
      } else {
        yield* [Transaction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Transaction {
    return {
      signatures: globalThis.Array.isArray(object?.signatures)
        ? object.signatures.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      message: isSet(object.message) ? Message.fromJSON(object.message) : undefined,
    };
  },

  toJSON(message: Transaction): unknown {
    const obj: any = {};
    if (message.signatures?.length) {
      obj.signatures = message.signatures.map((e) => base64FromBytes(e));
    }
    if (message.message !== undefined) {
      obj.message = Message.toJSON(message.message);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Transaction>, I>>(base?: I): Transaction {
    return Transaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Transaction>, I>>(object: I): Transaction {
    const message = createBaseTransaction();
    message.signatures = object.signatures?.map((e) => e) || [];
    message.message = (object.message !== undefined && object.message !== null)
      ? Message.fromPartial(object.message)
      : undefined;
    return message;
  },
};

function createBaseMessage(): Message {
  return {
    header: undefined,
    accountKeys: [],
    recentBlockhash: Buffer.alloc(0),
    instructions: [],
    versioned: false,
    addressTableLookups: [],
  };
}

export const Message: MessageFns<Message> = {
  encode(message: Message, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.header !== undefined) {
      MessageHeader.encode(message.header, writer.uint32(10).fork()).join();
    }
    for (const v of message.accountKeys) {
      writer.uint32(18).bytes(v!);
    }
    if (message.recentBlockhash.length !== 0) {
      writer.uint32(26).bytes(message.recentBlockhash);
    }
    for (const v of message.instructions) {
      CompiledInstruction.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.versioned !== false) {
      writer.uint32(40).bool(message.versioned);
    }
    for (const v of message.addressTableLookups) {
      MessageAddressTableLookup.encode(v!, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Message {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.header = MessageHeader.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountKeys.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.recentBlockhash = Buffer.from(reader.bytes());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.instructions.push(CompiledInstruction.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.versioned = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.addressTableLookups.push(MessageAddressTableLookup.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Message, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Message | Message[]> | Iterable<Message | Message[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Message.encode(p).finish()];
        }
      } else {
        yield* [Message.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Message>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Message> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Message.decode(p)];
        }
      } else {
        yield* [Message.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Message {
    return {
      header: isSet(object.header) ? MessageHeader.fromJSON(object.header) : undefined,
      accountKeys: globalThis.Array.isArray(object?.accountKeys)
        ? object.accountKeys.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      recentBlockhash: isSet(object.recentBlockhash)
        ? Buffer.from(bytesFromBase64(object.recentBlockhash))
        : Buffer.alloc(0),
      instructions: globalThis.Array.isArray(object?.instructions)
        ? object.instructions.map((e: any) => CompiledInstruction.fromJSON(e))
        : [],
      versioned: isSet(object.versioned) ? globalThis.Boolean(object.versioned) : false,
      addressTableLookups: globalThis.Array.isArray(object?.addressTableLookups)
        ? object.addressTableLookups.map((e: any) => MessageAddressTableLookup.fromJSON(e))
        : [],
    };
  },

  toJSON(message: Message): unknown {
    const obj: any = {};
    if (message.header !== undefined) {
      obj.header = MessageHeader.toJSON(message.header);
    }
    if (message.accountKeys?.length) {
      obj.accountKeys = message.accountKeys.map((e) => base64FromBytes(e));
    }
    if (message.recentBlockhash.length !== 0) {
      obj.recentBlockhash = base64FromBytes(message.recentBlockhash);
    }
    if (message.instructions?.length) {
      obj.instructions = message.instructions.map((e) => CompiledInstruction.toJSON(e));
    }
    if (message.versioned !== false) {
      obj.versioned = message.versioned;
    }
    if (message.addressTableLookups?.length) {
      obj.addressTableLookups = message.addressTableLookups.map((e) => MessageAddressTableLookup.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Message>, I>>(base?: I): Message {
    return Message.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Message>, I>>(object: I): Message {
    const message = createBaseMessage();
    message.header = (object.header !== undefined && object.header !== null)
      ? MessageHeader.fromPartial(object.header)
      : undefined;
    message.accountKeys = object.accountKeys?.map((e) => e) || [];
    message.recentBlockhash = object.recentBlockhash ?? Buffer.alloc(0);
    message.instructions = object.instructions?.map((e) => CompiledInstruction.fromPartial(e)) || [];
    message.versioned = object.versioned ?? false;
    message.addressTableLookups = object.addressTableLookups?.map((e) => MessageAddressTableLookup.fromPartial(e)) ||
      [];
    return message;
  },
};

function createBaseMessageHeader(): MessageHeader {
  return { numRequiredSignatures: 0, numReadonlySignedAccounts: 0, numReadonlyUnsignedAccounts: 0 };
}

export const MessageHeader: MessageFns<MessageHeader> = {
  encode(message: MessageHeader, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.numRequiredSignatures !== 0) {
      writer.uint32(8).uint32(message.numRequiredSignatures);
    }
    if (message.numReadonlySignedAccounts !== 0) {
      writer.uint32(16).uint32(message.numReadonlySignedAccounts);
    }
    if (message.numReadonlyUnsignedAccounts !== 0) {
      writer.uint32(24).uint32(message.numReadonlyUnsignedAccounts);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MessageHeader {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageHeader();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.numRequiredSignatures = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.numReadonlySignedAccounts = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.numReadonlyUnsignedAccounts = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<MessageHeader, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<MessageHeader | MessageHeader[]> | Iterable<MessageHeader | MessageHeader[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageHeader.encode(p).finish()];
        }
      } else {
        yield* [MessageHeader.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, MessageHeader>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<MessageHeader> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageHeader.decode(p)];
        }
      } else {
        yield* [MessageHeader.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): MessageHeader {
    return {
      numRequiredSignatures: isSet(object.numRequiredSignatures) ? globalThis.Number(object.numRequiredSignatures) : 0,
      numReadonlySignedAccounts: isSet(object.numReadonlySignedAccounts)
        ? globalThis.Number(object.numReadonlySignedAccounts)
        : 0,
      numReadonlyUnsignedAccounts: isSet(object.numReadonlyUnsignedAccounts)
        ? globalThis.Number(object.numReadonlyUnsignedAccounts)
        : 0,
    };
  },

  toJSON(message: MessageHeader): unknown {
    const obj: any = {};
    if (message.numRequiredSignatures !== 0) {
      obj.numRequiredSignatures = Math.round(message.numRequiredSignatures);
    }
    if (message.numReadonlySignedAccounts !== 0) {
      obj.numReadonlySignedAccounts = Math.round(message.numReadonlySignedAccounts);
    }
    if (message.numReadonlyUnsignedAccounts !== 0) {
      obj.numReadonlyUnsignedAccounts = Math.round(message.numReadonlyUnsignedAccounts);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageHeader>, I>>(base?: I): MessageHeader {
    return MessageHeader.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageHeader>, I>>(object: I): MessageHeader {
    const message = createBaseMessageHeader();
    message.numRequiredSignatures = object.numRequiredSignatures ?? 0;
    message.numReadonlySignedAccounts = object.numReadonlySignedAccounts ?? 0;
    message.numReadonlyUnsignedAccounts = object.numReadonlyUnsignedAccounts ?? 0;
    return message;
  },
};

function createBaseMessageAddressTableLookup(): MessageAddressTableLookup {
  return { accountKey: Buffer.alloc(0), writableIndexes: Buffer.alloc(0), readonlyIndexes: Buffer.alloc(0) };
}

export const MessageAddressTableLookup: MessageFns<MessageAddressTableLookup> = {
  encode(message: MessageAddressTableLookup, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountKey.length !== 0) {
      writer.uint32(10).bytes(message.accountKey);
    }
    if (message.writableIndexes.length !== 0) {
      writer.uint32(18).bytes(message.writableIndexes);
    }
    if (message.readonlyIndexes.length !== 0) {
      writer.uint32(26).bytes(message.readonlyIndexes);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MessageAddressTableLookup {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageAddressTableLookup();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountKey = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.writableIndexes = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.readonlyIndexes = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<MessageAddressTableLookup, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<MessageAddressTableLookup | MessageAddressTableLookup[]>
      | Iterable<MessageAddressTableLookup | MessageAddressTableLookup[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageAddressTableLookup.encode(p).finish()];
        }
      } else {
        yield* [MessageAddressTableLookup.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, MessageAddressTableLookup>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<MessageAddressTableLookup> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageAddressTableLookup.decode(p)];
        }
      } else {
        yield* [MessageAddressTableLookup.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): MessageAddressTableLookup {
    return {
      accountKey: isSet(object.accountKey) ? Buffer.from(bytesFromBase64(object.accountKey)) : Buffer.alloc(0),
      writableIndexes: isSet(object.writableIndexes)
        ? Buffer.from(bytesFromBase64(object.writableIndexes))
        : Buffer.alloc(0),
      readonlyIndexes: isSet(object.readonlyIndexes)
        ? Buffer.from(bytesFromBase64(object.readonlyIndexes))
        : Buffer.alloc(0),
    };
  },

  toJSON(message: MessageAddressTableLookup): unknown {
    const obj: any = {};
    if (message.accountKey.length !== 0) {
      obj.accountKey = base64FromBytes(message.accountKey);
    }
    if (message.writableIndexes.length !== 0) {
      obj.writableIndexes = base64FromBytes(message.writableIndexes);
    }
    if (message.readonlyIndexes.length !== 0) {
      obj.readonlyIndexes = base64FromBytes(message.readonlyIndexes);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageAddressTableLookup>, I>>(base?: I): MessageAddressTableLookup {
    return MessageAddressTableLookup.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageAddressTableLookup>, I>>(object: I): MessageAddressTableLookup {
    const message = createBaseMessageAddressTableLookup();
    message.accountKey = object.accountKey ?? Buffer.alloc(0);
    message.writableIndexes = object.writableIndexes ?? Buffer.alloc(0);
    message.readonlyIndexes = object.readonlyIndexes ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseTransactionStatusMeta(): TransactionStatusMeta {
  return {
    err: undefined,
    fee: 0n,
    preBalances: [],
    postBalances: [],
    innerInstructions: [],
    innerInstructionsNone: false,
    logMessages: [],
    logMessagesNone: false,
    preTokenBalances: [],
    postTokenBalances: [],
    rewards: [],
    loadedWritableAddresses: [],
    loadedReadonlyAddresses: [],
    returnData: undefined,
    returnDataNone: false,
    computeUnitsConsumed: undefined,
  };
}

export const TransactionStatusMeta: MessageFns<TransactionStatusMeta> = {
  encode(message: TransactionStatusMeta, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.err !== undefined) {
      TransactionError.encode(message.err, writer.uint32(10).fork()).join();
    }
    if (message.fee !== 0n) {
      if (BigInt.asUintN(64, message.fee) !== message.fee) {
        throw new globalThis.Error("value provided for field message.fee of type uint64 too large");
      }
      writer.uint32(16).uint64(message.fee);
    }
    writer.uint32(26).fork();
    for (const v of message.preBalances) {
      if (BigInt.asUintN(64, v) !== v) {
        throw new globalThis.Error("a value provided in array field preBalances of type uint64 is too large");
      }
      writer.uint64(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.postBalances) {
      if (BigInt.asUintN(64, v) !== v) {
        throw new globalThis.Error("a value provided in array field postBalances of type uint64 is too large");
      }
      writer.uint64(v);
    }
    writer.join();
    for (const v of message.innerInstructions) {
      InnerInstructions.encode(v!, writer.uint32(42).fork()).join();
    }
    if (message.innerInstructionsNone !== false) {
      writer.uint32(80).bool(message.innerInstructionsNone);
    }
    for (const v of message.logMessages) {
      writer.uint32(50).string(v!);
    }
    if (message.logMessagesNone !== false) {
      writer.uint32(88).bool(message.logMessagesNone);
    }
    for (const v of message.preTokenBalances) {
      TokenBalance.encode(v!, writer.uint32(58).fork()).join();
    }
    for (const v of message.postTokenBalances) {
      TokenBalance.encode(v!, writer.uint32(66).fork()).join();
    }
    for (const v of message.rewards) {
      Reward.encode(v!, writer.uint32(74).fork()).join();
    }
    for (const v of message.loadedWritableAddresses) {
      writer.uint32(98).bytes(v!);
    }
    for (const v of message.loadedReadonlyAddresses) {
      writer.uint32(106).bytes(v!);
    }
    if (message.returnData !== undefined) {
      ReturnData.encode(message.returnData, writer.uint32(114).fork()).join();
    }
    if (message.returnDataNone !== false) {
      writer.uint32(120).bool(message.returnDataNone);
    }
    if (message.computeUnitsConsumed !== undefined) {
      if (BigInt.asUintN(64, message.computeUnitsConsumed) !== message.computeUnitsConsumed) {
        throw new globalThis.Error("value provided for field message.computeUnitsConsumed of type uint64 too large");
      }
      writer.uint32(128).uint64(message.computeUnitsConsumed);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TransactionStatusMeta {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransactionStatusMeta();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.err = TransactionError.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.fee = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.preBalances.push(reader.uint64() as bigint);

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.preBalances.push(reader.uint64() as bigint);
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 32) {
            message.postBalances.push(reader.uint64() as bigint);

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.postBalances.push(reader.uint64() as bigint);
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.innerInstructions.push(InnerInstructions.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.innerInstructionsNone = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.logMessages.push(reader.string());
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.logMessagesNone = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.preTokenBalances.push(TokenBalance.decode(reader, reader.uint32()));
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.postTokenBalances.push(TokenBalance.decode(reader, reader.uint32()));
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.rewards.push(Reward.decode(reader, reader.uint32()));
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.loadedWritableAddresses.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.loadedReadonlyAddresses.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.returnData = ReturnData.decode(reader, reader.uint32());
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.returnDataNone = reader.bool();
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.computeUnitsConsumed = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<TransactionStatusMeta, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<TransactionStatusMeta | TransactionStatusMeta[]>
      | Iterable<TransactionStatusMeta | TransactionStatusMeta[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TransactionStatusMeta.encode(p).finish()];
        }
      } else {
        yield* [TransactionStatusMeta.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, TransactionStatusMeta>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<TransactionStatusMeta> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TransactionStatusMeta.decode(p)];
        }
      } else {
        yield* [TransactionStatusMeta.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): TransactionStatusMeta {
    return {
      err: isSet(object.err) ? TransactionError.fromJSON(object.err) : undefined,
      fee: isSet(object.fee) ? BigInt(object.fee) : 0n,
      preBalances: globalThis.Array.isArray(object?.preBalances) ? object.preBalances.map((e: any) => BigInt(e)) : [],
      postBalances: globalThis.Array.isArray(object?.postBalances)
        ? object.postBalances.map((e: any) => BigInt(e))
        : [],
      innerInstructions: globalThis.Array.isArray(object?.innerInstructions)
        ? object.innerInstructions.map((e: any) => InnerInstructions.fromJSON(e))
        : [],
      innerInstructionsNone: isSet(object.innerInstructionsNone)
        ? globalThis.Boolean(object.innerInstructionsNone)
        : false,
      logMessages: globalThis.Array.isArray(object?.logMessages)
        ? object.logMessages.map((e: any) => globalThis.String(e))
        : [],
      logMessagesNone: isSet(object.logMessagesNone) ? globalThis.Boolean(object.logMessagesNone) : false,
      preTokenBalances: globalThis.Array.isArray(object?.preTokenBalances)
        ? object.preTokenBalances.map((e: any) => TokenBalance.fromJSON(e))
        : [],
      postTokenBalances: globalThis.Array.isArray(object?.postTokenBalances)
        ? object.postTokenBalances.map((e: any) => TokenBalance.fromJSON(e))
        : [],
      rewards: globalThis.Array.isArray(object?.rewards) ? object.rewards.map((e: any) => Reward.fromJSON(e)) : [],
      loadedWritableAddresses: globalThis.Array.isArray(object?.loadedWritableAddresses)
        ? object.loadedWritableAddresses.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      loadedReadonlyAddresses: globalThis.Array.isArray(object?.loadedReadonlyAddresses)
        ? object.loadedReadonlyAddresses.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      returnData: isSet(object.returnData) ? ReturnData.fromJSON(object.returnData) : undefined,
      returnDataNone: isSet(object.returnDataNone) ? globalThis.Boolean(object.returnDataNone) : false,
      computeUnitsConsumed: isSet(object.computeUnitsConsumed) ? BigInt(object.computeUnitsConsumed) : undefined,
    };
  },

  toJSON(message: TransactionStatusMeta): unknown {
    const obj: any = {};
    if (message.err !== undefined) {
      obj.err = TransactionError.toJSON(message.err);
    }
    if (message.fee !== 0n) {
      obj.fee = message.fee.toString();
    }
    if (message.preBalances?.length) {
      obj.preBalances = message.preBalances.map((e) => e.toString());
    }
    if (message.postBalances?.length) {
      obj.postBalances = message.postBalances.map((e) => e.toString());
    }
    if (message.innerInstructions?.length) {
      obj.innerInstructions = message.innerInstructions.map((e) => InnerInstructions.toJSON(e));
    }
    if (message.innerInstructionsNone !== false) {
      obj.innerInstructionsNone = message.innerInstructionsNone;
    }
    if (message.logMessages?.length) {
      obj.logMessages = message.logMessages;
    }
    if (message.logMessagesNone !== false) {
      obj.logMessagesNone = message.logMessagesNone;
    }
    if (message.preTokenBalances?.length) {
      obj.preTokenBalances = message.preTokenBalances.map((e) => TokenBalance.toJSON(e));
    }
    if (message.postTokenBalances?.length) {
      obj.postTokenBalances = message.postTokenBalances.map((e) => TokenBalance.toJSON(e));
    }
    if (message.rewards?.length) {
      obj.rewards = message.rewards.map((e) => Reward.toJSON(e));
    }
    if (message.loadedWritableAddresses?.length) {
      obj.loadedWritableAddresses = message.loadedWritableAddresses.map((e) => base64FromBytes(e));
    }
    if (message.loadedReadonlyAddresses?.length) {
      obj.loadedReadonlyAddresses = message.loadedReadonlyAddresses.map((e) => base64FromBytes(e));
    }
    if (message.returnData !== undefined) {
      obj.returnData = ReturnData.toJSON(message.returnData);
    }
    if (message.returnDataNone !== false) {
      obj.returnDataNone = message.returnDataNone;
    }
    if (message.computeUnitsConsumed !== undefined) {
      obj.computeUnitsConsumed = message.computeUnitsConsumed.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TransactionStatusMeta>, I>>(base?: I): TransactionStatusMeta {
    return TransactionStatusMeta.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransactionStatusMeta>, I>>(object: I): TransactionStatusMeta {
    const message = createBaseTransactionStatusMeta();
    message.err = (object.err !== undefined && object.err !== null)
      ? TransactionError.fromPartial(object.err)
      : undefined;
    message.fee = object.fee ?? 0n;
    message.preBalances = object.preBalances?.map((e) => e) || [];
    message.postBalances = object.postBalances?.map((e) => e) || [];
    message.innerInstructions = object.innerInstructions?.map((e) => InnerInstructions.fromPartial(e)) || [];
    message.innerInstructionsNone = object.innerInstructionsNone ?? false;
    message.logMessages = object.logMessages?.map((e) => e) || [];
    message.logMessagesNone = object.logMessagesNone ?? false;
    message.preTokenBalances = object.preTokenBalances?.map((e) => TokenBalance.fromPartial(e)) || [];
    message.postTokenBalances = object.postTokenBalances?.map((e) => TokenBalance.fromPartial(e)) || [];
    message.rewards = object.rewards?.map((e) => Reward.fromPartial(e)) || [];
    message.loadedWritableAddresses = object.loadedWritableAddresses?.map((e) => e) || [];
    message.loadedReadonlyAddresses = object.loadedReadonlyAddresses?.map((e) => e) || [];
    message.returnData = (object.returnData !== undefined && object.returnData !== null)
      ? ReturnData.fromPartial(object.returnData)
      : undefined;
    message.returnDataNone = object.returnDataNone ?? false;
    message.computeUnitsConsumed = object.computeUnitsConsumed ?? undefined;
    return message;
  },
};

function createBaseTransactionError(): TransactionError {
  return { err: Buffer.alloc(0) };
}

export const TransactionError: MessageFns<TransactionError> = {
  encode(message: TransactionError, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.err.length !== 0) {
      writer.uint32(10).bytes(message.err);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TransactionError {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransactionError();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.err = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<TransactionError, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<TransactionError | TransactionError[]> | Iterable<TransactionError | TransactionError[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TransactionError.encode(p).finish()];
        }
      } else {
        yield* [TransactionError.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, TransactionError>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<TransactionError> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TransactionError.decode(p)];
        }
      } else {
        yield* [TransactionError.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): TransactionError {
    return { err: isSet(object.err) ? Buffer.from(bytesFromBase64(object.err)) : Buffer.alloc(0) };
  },

  toJSON(message: TransactionError): unknown {
    const obj: any = {};
    if (message.err.length !== 0) {
      obj.err = base64FromBytes(message.err);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TransactionError>, I>>(base?: I): TransactionError {
    return TransactionError.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransactionError>, I>>(object: I): TransactionError {
    const message = createBaseTransactionError();
    message.err = object.err ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseInnerInstructions(): InnerInstructions {
  return { index: 0, instructions: [] };
}

export const InnerInstructions: MessageFns<InnerInstructions> = {
  encode(message: InnerInstructions, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.index !== 0) {
      writer.uint32(8).uint32(message.index);
    }
    for (const v of message.instructions) {
      InnerInstruction.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InnerInstructions {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInnerInstructions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.index = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.instructions.push(InnerInstruction.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<InnerInstructions, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<InnerInstructions | InnerInstructions[]> | Iterable<InnerInstructions | InnerInstructions[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [InnerInstructions.encode(p).finish()];
        }
      } else {
        yield* [InnerInstructions.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, InnerInstructions>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<InnerInstructions> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [InnerInstructions.decode(p)];
        }
      } else {
        yield* [InnerInstructions.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): InnerInstructions {
    return {
      index: isSet(object.index) ? globalThis.Number(object.index) : 0,
      instructions: globalThis.Array.isArray(object?.instructions)
        ? object.instructions.map((e: any) => InnerInstruction.fromJSON(e))
        : [],
    };
  },

  toJSON(message: InnerInstructions): unknown {
    const obj: any = {};
    if (message.index !== 0) {
      obj.index = Math.round(message.index);
    }
    if (message.instructions?.length) {
      obj.instructions = message.instructions.map((e) => InnerInstruction.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InnerInstructions>, I>>(base?: I): InnerInstructions {
    return InnerInstructions.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InnerInstructions>, I>>(object: I): InnerInstructions {
    const message = createBaseInnerInstructions();
    message.index = object.index ?? 0;
    message.instructions = object.instructions?.map((e) => InnerInstruction.fromPartial(e)) || [];
    return message;
  },
};

function createBaseInnerInstruction(): InnerInstruction {
  return { programIdIndex: 0, accounts: Buffer.alloc(0), data: Buffer.alloc(0), stackHeight: undefined };
}

export const InnerInstruction: MessageFns<InnerInstruction> = {
  encode(message: InnerInstruction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.programIdIndex !== 0) {
      writer.uint32(8).uint32(message.programIdIndex);
    }
    if (message.accounts.length !== 0) {
      writer.uint32(18).bytes(message.accounts);
    }
    if (message.data.length !== 0) {
      writer.uint32(26).bytes(message.data);
    }
    if (message.stackHeight !== undefined) {
      writer.uint32(32).uint32(message.stackHeight);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InnerInstruction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInnerInstruction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.programIdIndex = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accounts = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.data = Buffer.from(reader.bytes());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.stackHeight = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<InnerInstruction, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<InnerInstruction | InnerInstruction[]> | Iterable<InnerInstruction | InnerInstruction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [InnerInstruction.encode(p).finish()];
        }
      } else {
        yield* [InnerInstruction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, InnerInstruction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<InnerInstruction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [InnerInstruction.decode(p)];
        }
      } else {
        yield* [InnerInstruction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): InnerInstruction {
    return {
      programIdIndex: isSet(object.programIdIndex) ? globalThis.Number(object.programIdIndex) : 0,
      accounts: isSet(object.accounts) ? Buffer.from(bytesFromBase64(object.accounts)) : Buffer.alloc(0),
      data: isSet(object.data) ? Buffer.from(bytesFromBase64(object.data)) : Buffer.alloc(0),
      stackHeight: isSet(object.stackHeight) ? globalThis.Number(object.stackHeight) : undefined,
    };
  },

  toJSON(message: InnerInstruction): unknown {
    const obj: any = {};
    if (message.programIdIndex !== 0) {
      obj.programIdIndex = Math.round(message.programIdIndex);
    }
    if (message.accounts.length !== 0) {
      obj.accounts = base64FromBytes(message.accounts);
    }
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    if (message.stackHeight !== undefined) {
      obj.stackHeight = Math.round(message.stackHeight);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InnerInstruction>, I>>(base?: I): InnerInstruction {
    return InnerInstruction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InnerInstruction>, I>>(object: I): InnerInstruction {
    const message = createBaseInnerInstruction();
    message.programIdIndex = object.programIdIndex ?? 0;
    message.accounts = object.accounts ?? Buffer.alloc(0);
    message.data = object.data ?? Buffer.alloc(0);
    message.stackHeight = object.stackHeight ?? undefined;
    return message;
  },
};

function createBaseCompiledInstruction(): CompiledInstruction {
  return { programIdIndex: 0, accounts: Buffer.alloc(0), data: Buffer.alloc(0) };
}

export const CompiledInstruction: MessageFns<CompiledInstruction> = {
  encode(message: CompiledInstruction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.programIdIndex !== 0) {
      writer.uint32(8).uint32(message.programIdIndex);
    }
    if (message.accounts.length !== 0) {
      writer.uint32(18).bytes(message.accounts);
    }
    if (message.data.length !== 0) {
      writer.uint32(26).bytes(message.data);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompiledInstruction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompiledInstruction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.programIdIndex = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accounts = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.data = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<CompiledInstruction, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<CompiledInstruction | CompiledInstruction[]>
      | Iterable<CompiledInstruction | CompiledInstruction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [CompiledInstruction.encode(p).finish()];
        }
      } else {
        yield* [CompiledInstruction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, CompiledInstruction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<CompiledInstruction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [CompiledInstruction.decode(p)];
        }
      } else {
        yield* [CompiledInstruction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): CompiledInstruction {
    return {
      programIdIndex: isSet(object.programIdIndex) ? globalThis.Number(object.programIdIndex) : 0,
      accounts: isSet(object.accounts) ? Buffer.from(bytesFromBase64(object.accounts)) : Buffer.alloc(0),
      data: isSet(object.data) ? Buffer.from(bytesFromBase64(object.data)) : Buffer.alloc(0),
    };
  },

  toJSON(message: CompiledInstruction): unknown {
    const obj: any = {};
    if (message.programIdIndex !== 0) {
      obj.programIdIndex = Math.round(message.programIdIndex);
    }
    if (message.accounts.length !== 0) {
      obj.accounts = base64FromBytes(message.accounts);
    }
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompiledInstruction>, I>>(base?: I): CompiledInstruction {
    return CompiledInstruction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompiledInstruction>, I>>(object: I): CompiledInstruction {
    const message = createBaseCompiledInstruction();
    message.programIdIndex = object.programIdIndex ?? 0;
    message.accounts = object.accounts ?? Buffer.alloc(0);
    message.data = object.data ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseTokenBalance(): TokenBalance {
  return { accountIndex: 0, mint: "", uiTokenAmount: undefined, owner: "", programId: "" };
}

export const TokenBalance: MessageFns<TokenBalance> = {
  encode(message: TokenBalance, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountIndex !== 0) {
      writer.uint32(8).uint32(message.accountIndex);
    }
    if (message.mint !== "") {
      writer.uint32(18).string(message.mint);
    }
    if (message.uiTokenAmount !== undefined) {
      UiTokenAmount.encode(message.uiTokenAmount, writer.uint32(26).fork()).join();
    }
    if (message.owner !== "") {
      writer.uint32(34).string(message.owner);
    }
    if (message.programId !== "") {
      writer.uint32(42).string(message.programId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TokenBalance {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTokenBalance();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.accountIndex = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.mint = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.uiTokenAmount = UiTokenAmount.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.owner = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.programId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<TokenBalance, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<TokenBalance | TokenBalance[]> | Iterable<TokenBalance | TokenBalance[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TokenBalance.encode(p).finish()];
        }
      } else {
        yield* [TokenBalance.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, TokenBalance>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<TokenBalance> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TokenBalance.decode(p)];
        }
      } else {
        yield* [TokenBalance.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): TokenBalance {
    return {
      accountIndex: isSet(object.accountIndex) ? globalThis.Number(object.accountIndex) : 0,
      mint: isSet(object.mint) ? globalThis.String(object.mint) : "",
      uiTokenAmount: isSet(object.uiTokenAmount) ? UiTokenAmount.fromJSON(object.uiTokenAmount) : undefined,
      owner: isSet(object.owner) ? globalThis.String(object.owner) : "",
      programId: isSet(object.programId) ? globalThis.String(object.programId) : "",
    };
  },

  toJSON(message: TokenBalance): unknown {
    const obj: any = {};
    if (message.accountIndex !== 0) {
      obj.accountIndex = Math.round(message.accountIndex);
    }
    if (message.mint !== "") {
      obj.mint = message.mint;
    }
    if (message.uiTokenAmount !== undefined) {
      obj.uiTokenAmount = UiTokenAmount.toJSON(message.uiTokenAmount);
    }
    if (message.owner !== "") {
      obj.owner = message.owner;
    }
    if (message.programId !== "") {
      obj.programId = message.programId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TokenBalance>, I>>(base?: I): TokenBalance {
    return TokenBalance.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TokenBalance>, I>>(object: I): TokenBalance {
    const message = createBaseTokenBalance();
    message.accountIndex = object.accountIndex ?? 0;
    message.mint = object.mint ?? "";
    message.uiTokenAmount = (object.uiTokenAmount !== undefined && object.uiTokenAmount !== null)
      ? UiTokenAmount.fromPartial(object.uiTokenAmount)
      : undefined;
    message.owner = object.owner ?? "";
    message.programId = object.programId ?? "";
    return message;
  },
};

function createBaseUiTokenAmount(): UiTokenAmount {
  return { uiAmount: 0, decimals: 0, amount: "", uiAmountString: "" };
}

export const UiTokenAmount: MessageFns<UiTokenAmount> = {
  encode(message: UiTokenAmount, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uiAmount !== 0) {
      writer.uint32(9).double(message.uiAmount);
    }
    if (message.decimals !== 0) {
      writer.uint32(16).uint32(message.decimals);
    }
    if (message.amount !== "") {
      writer.uint32(26).string(message.amount);
    }
    if (message.uiAmountString !== "") {
      writer.uint32(34).string(message.uiAmountString);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UiTokenAmount {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUiTokenAmount();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.uiAmount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.decimals = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.amount = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.uiAmountString = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<UiTokenAmount, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<UiTokenAmount | UiTokenAmount[]> | Iterable<UiTokenAmount | UiTokenAmount[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [UiTokenAmount.encode(p).finish()];
        }
      } else {
        yield* [UiTokenAmount.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, UiTokenAmount>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<UiTokenAmount> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [UiTokenAmount.decode(p)];
        }
      } else {
        yield* [UiTokenAmount.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): UiTokenAmount {
    return {
      uiAmount: isSet(object.uiAmount) ? globalThis.Number(object.uiAmount) : 0,
      decimals: isSet(object.decimals) ? globalThis.Number(object.decimals) : 0,
      amount: isSet(object.amount) ? globalThis.String(object.amount) : "",
      uiAmountString: isSet(object.uiAmountString) ? globalThis.String(object.uiAmountString) : "",
    };
  },

  toJSON(message: UiTokenAmount): unknown {
    const obj: any = {};
    if (message.uiAmount !== 0) {
      obj.uiAmount = message.uiAmount;
    }
    if (message.decimals !== 0) {
      obj.decimals = Math.round(message.decimals);
    }
    if (message.amount !== "") {
      obj.amount = message.amount;
    }
    if (message.uiAmountString !== "") {
      obj.uiAmountString = message.uiAmountString;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UiTokenAmount>, I>>(base?: I): UiTokenAmount {
    return UiTokenAmount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UiTokenAmount>, I>>(object: I): UiTokenAmount {
    const message = createBaseUiTokenAmount();
    message.uiAmount = object.uiAmount ?? 0;
    message.decimals = object.decimals ?? 0;
    message.amount = object.amount ?? "";
    message.uiAmountString = object.uiAmountString ?? "";
    return message;
  },
};

function createBaseReturnData(): ReturnData {
  return { programId: Buffer.alloc(0), data: Buffer.alloc(0) };
}

export const ReturnData: MessageFns<ReturnData> = {
  encode(message: ReturnData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.programId.length !== 0) {
      writer.uint32(10).bytes(message.programId);
    }
    if (message.data.length !== 0) {
      writer.uint32(18).bytes(message.data);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReturnData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReturnData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.programId = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.data = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<ReturnData, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<ReturnData | ReturnData[]> | Iterable<ReturnData | ReturnData[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ReturnData.encode(p).finish()];
        }
      } else {
        yield* [ReturnData.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, ReturnData>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<ReturnData> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ReturnData.decode(p)];
        }
      } else {
        yield* [ReturnData.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): ReturnData {
    return {
      programId: isSet(object.programId) ? Buffer.from(bytesFromBase64(object.programId)) : Buffer.alloc(0),
      data: isSet(object.data) ? Buffer.from(bytesFromBase64(object.data)) : Buffer.alloc(0),
    };
  },

  toJSON(message: ReturnData): unknown {
    const obj: any = {};
    if (message.programId.length !== 0) {
      obj.programId = base64FromBytes(message.programId);
    }
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReturnData>, I>>(base?: I): ReturnData {
    return ReturnData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReturnData>, I>>(object: I): ReturnData {
    const message = createBaseReturnData();
    message.programId = object.programId ?? Buffer.alloc(0);
    message.data = object.data ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseReward(): Reward {
  return { pubkey: "", lamports: 0n, postBalance: 0n, rewardType: 0, commission: "" };
}

export const Reward: MessageFns<Reward> = {
  encode(message: Reward, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pubkey !== "") {
      writer.uint32(10).string(message.pubkey);
    }
    if (message.lamports !== 0n) {
      if (BigInt.asIntN(64, message.lamports) !== message.lamports) {
        throw new globalThis.Error("value provided for field message.lamports of type int64 too large");
      }
      writer.uint32(16).int64(message.lamports);
    }
    if (message.postBalance !== 0n) {
      if (BigInt.asUintN(64, message.postBalance) !== message.postBalance) {
        throw new globalThis.Error("value provided for field message.postBalance of type uint64 too large");
      }
      writer.uint32(24).uint64(message.postBalance);
    }
    if (message.rewardType !== 0) {
      writer.uint32(32).int32(message.rewardType);
    }
    if (message.commission !== "") {
      writer.uint32(42).string(message.commission);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Reward {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReward();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pubkey = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lamports = reader.int64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.postBalance = reader.uint64() as bigint;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.rewardType = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.commission = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Reward, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Reward | Reward[]> | Iterable<Reward | Reward[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Reward.encode(p).finish()];
        }
      } else {
        yield* [Reward.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Reward>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Reward> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Reward.decode(p)];
        }
      } else {
        yield* [Reward.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Reward {
    return {
      pubkey: isSet(object.pubkey) ? globalThis.String(object.pubkey) : "",
      lamports: isSet(object.lamports) ? BigInt(object.lamports) : 0n,
      postBalance: isSet(object.postBalance) ? BigInt(object.postBalance) : 0n,
      rewardType: isSet(object.rewardType) ? rewardTypeFromJSON(object.rewardType) : 0,
      commission: isSet(object.commission) ? globalThis.String(object.commission) : "",
    };
  },

  toJSON(message: Reward): unknown {
    const obj: any = {};
    if (message.pubkey !== "") {
      obj.pubkey = message.pubkey;
    }
    if (message.lamports !== 0n) {
      obj.lamports = message.lamports.toString();
    }
    if (message.postBalance !== 0n) {
      obj.postBalance = message.postBalance.toString();
    }
    if (message.rewardType !== 0) {
      obj.rewardType = rewardTypeToJSON(message.rewardType);
    }
    if (message.commission !== "") {
      obj.commission = message.commission;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Reward>, I>>(base?: I): Reward {
    return Reward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Reward>, I>>(object: I): Reward {
    const message = createBaseReward();
    message.pubkey = object.pubkey ?? "";
    message.lamports = object.lamports ?? 0n;
    message.postBalance = object.postBalance ?? 0n;
    message.rewardType = object.rewardType ?? 0;
    message.commission = object.commission ?? "";
    return message;
  },
};

function createBaseRewards(): Rewards {
  return { rewards: [], numPartitions: undefined };
}

export const Rewards: MessageFns<Rewards> = {
  encode(message: Rewards, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.rewards) {
      Reward.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.numPartitions !== undefined) {
      NumPartitions.encode(message.numPartitions, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Rewards {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRewards();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.rewards.push(Reward.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.numPartitions = NumPartitions.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Rewards, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Rewards | Rewards[]> | Iterable<Rewards | Rewards[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Rewards.encode(p).finish()];
        }
      } else {
        yield* [Rewards.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Rewards>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Rewards> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Rewards.decode(p)];
        }
      } else {
        yield* [Rewards.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Rewards {
    return {
      rewards: globalThis.Array.isArray(object?.rewards) ? object.rewards.map((e: any) => Reward.fromJSON(e)) : [],
      numPartitions: isSet(object.numPartitions) ? NumPartitions.fromJSON(object.numPartitions) : undefined,
    };
  },

  toJSON(message: Rewards): unknown {
    const obj: any = {};
    if (message.rewards?.length) {
      obj.rewards = message.rewards.map((e) => Reward.toJSON(e));
    }
    if (message.numPartitions !== undefined) {
      obj.numPartitions = NumPartitions.toJSON(message.numPartitions);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Rewards>, I>>(base?: I): Rewards {
    return Rewards.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Rewards>, I>>(object: I): Rewards {
    const message = createBaseRewards();
    message.rewards = object.rewards?.map((e) => Reward.fromPartial(e)) || [];
    message.numPartitions = (object.numPartitions !== undefined && object.numPartitions !== null)
      ? NumPartitions.fromPartial(object.numPartitions)
      : undefined;
    return message;
  },
};

function createBaseUnixTimestamp(): UnixTimestamp {
  return { timestamp: 0n };
}

export const UnixTimestamp: MessageFns<UnixTimestamp> = {
  encode(message: UnixTimestamp, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.timestamp !== 0n) {
      if (BigInt.asIntN(64, message.timestamp) !== message.timestamp) {
        throw new globalThis.Error("value provided for field message.timestamp of type int64 too large");
      }
      writer.uint32(8).int64(message.timestamp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UnixTimestamp {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUnixTimestamp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.timestamp = reader.int64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<UnixTimestamp, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<UnixTimestamp | UnixTimestamp[]> | Iterable<UnixTimestamp | UnixTimestamp[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [UnixTimestamp.encode(p).finish()];
        }
      } else {
        yield* [UnixTimestamp.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, UnixTimestamp>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<UnixTimestamp> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [UnixTimestamp.decode(p)];
        }
      } else {
        yield* [UnixTimestamp.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): UnixTimestamp {
    return { timestamp: isSet(object.timestamp) ? BigInt(object.timestamp) : 0n };
  },

  toJSON(message: UnixTimestamp): unknown {
    const obj: any = {};
    if (message.timestamp !== 0n) {
      obj.timestamp = message.timestamp.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UnixTimestamp>, I>>(base?: I): UnixTimestamp {
    return UnixTimestamp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnixTimestamp>, I>>(object: I): UnixTimestamp {
    const message = createBaseUnixTimestamp();
    message.timestamp = object.timestamp ?? 0n;
    return message;
  },
};

function createBaseBlockHeight(): BlockHeight {
  return { blockHeight: 0n };
}

export const BlockHeight: MessageFns<BlockHeight> = {
  encode(message: BlockHeight, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.blockHeight !== 0n) {
      if (BigInt.asUintN(64, message.blockHeight) !== message.blockHeight) {
        throw new globalThis.Error("value provided for field message.blockHeight of type uint64 too large");
      }
      writer.uint32(8).uint64(message.blockHeight);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BlockHeight {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBlockHeight();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.blockHeight = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<BlockHeight, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<BlockHeight | BlockHeight[]> | Iterable<BlockHeight | BlockHeight[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [BlockHeight.encode(p).finish()];
        }
      } else {
        yield* [BlockHeight.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, BlockHeight>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<BlockHeight> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [BlockHeight.decode(p)];
        }
      } else {
        yield* [BlockHeight.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): BlockHeight {
    return { blockHeight: isSet(object.blockHeight) ? BigInt(object.blockHeight) : 0n };
  },

  toJSON(message: BlockHeight): unknown {
    const obj: any = {};
    if (message.blockHeight !== 0n) {
      obj.blockHeight = message.blockHeight.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BlockHeight>, I>>(base?: I): BlockHeight {
    return BlockHeight.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BlockHeight>, I>>(object: I): BlockHeight {
    const message = createBaseBlockHeight();
    message.blockHeight = object.blockHeight ?? 0n;
    return message;
  },
};

function createBaseNumPartitions(): NumPartitions {
  return { numPartitions: 0n };
}

export const NumPartitions: MessageFns<NumPartitions> = {
  encode(message: NumPartitions, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.numPartitions !== 0n) {
      if (BigInt.asUintN(64, message.numPartitions) !== message.numPartitions) {
        throw new globalThis.Error("value provided for field message.numPartitions of type uint64 too large");
      }
      writer.uint32(8).uint64(message.numPartitions);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NumPartitions {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNumPartitions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.numPartitions = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<NumPartitions, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<NumPartitions | NumPartitions[]> | Iterable<NumPartitions | NumPartitions[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [NumPartitions.encode(p).finish()];
        }
      } else {
        yield* [NumPartitions.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, NumPartitions>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<NumPartitions> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [NumPartitions.decode(p)];
        }
      } else {
        yield* [NumPartitions.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): NumPartitions {
    return { numPartitions: isSet(object.numPartitions) ? BigInt(object.numPartitions) : 0n };
  },

  toJSON(message: NumPartitions): unknown {
    const obj: any = {};
    if (message.numPartitions !== 0n) {
      obj.numPartitions = message.numPartitions.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NumPartitions>, I>>(base?: I): NumPartitions {
    return NumPartitions.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NumPartitions>, I>>(object: I): NumPartitions {
    const message = createBaseNumPartitions();
    message.numPartitions = object.numPartitions ?? 0n;
    return message;
  },
};

function bytesFromBase64(b64: string): Uint8Array {
  return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}

function base64FromBytes(arr: Uint8Array): string {
  return globalThis.Buffer.from(arr).toString("base64");
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
