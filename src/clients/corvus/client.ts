import { type CreateGrpcClientOptions, createGrpcClient } from '../../utils'
import { ARPCServiceClient } from './generated/arpc'

export type CorvusArpcClientOptions = CreateGrpcClientOptions

export class CorvusArpcClient {
    protected readonly grpc: ARPCServiceClient

    public constructor(url: string, protected readonly options: CorvusArpcClientOptions = {}) {
        this.grpc = createGrpcClient(ARPCServiceClient, url, options)
    }
}
