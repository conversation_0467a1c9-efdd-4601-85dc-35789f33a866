import type { ClientDuplexStream } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import { type Awaitable, type DeferredPromise, createDeferred, withTimeout } from '@kdt310722/utils/promise'
import { StreamError } from '../../errors'
import { StreamWrapper, type StreamWrapperOptions, type StreamWrapperTimeoutOptions } from './stream-wrapper'
import type { OnStreamChangeFn } from './types'

export interface DuplexStreamWrapperTimeoutOptions extends StreamWrapperTimeoutOptions {
    end?: number
    drain?: number
    write?: number
}

export interface DuplexStreamWrapperOptions extends Omit<StreamWrapperOptions, 'timeout'> {
    timeout?: DuplexStreamWrapperTimeoutOptions
}

export class DuplexStreamWrapper<TRequest, TResponse, TStream extends ClientDuplexStream<TRequest, TResponse> = ClientDuplexStream<TRequest, TResponse>> extends StreamWrapper<TResponse, TStream> {
    protected readonly endTimeout: number
    protected readonly drainTimeout: number
    protected readonly writeTimeout: number

    public constructor(protected override readonly subscriber: () => Awaitable<TStream>, options: DuplexStreamWrapperOptions = {}) {
        super(subscriber, options)

        this.endTimeout = options.timeout?.end ?? 10_000
        this.drainTimeout = options.timeout?.drain ?? 10_000
        this.writeTimeout = options.timeout?.write ?? 10_000
    }

    public async write(data: TRequest) {
        const stream = await this.getStream().then((stream) => this.waitForWritable(stream))
        const wrote = createDeferred<void>()
        const result = stream.write(data, (error?: Error) => (notNullish(error) ? wrote.reject(error) : wrote.resolve()))

        if (!result && !wrote.isSettled) {
            wrote.reject(new StreamError('Stream is not writable').withStream(stream))
        }

        return withTimeout(wrote, this.writeTimeout, () => new StreamError('Write timeout').withStream(stream))
    }

    protected async waitForWritable(stream: TStream) {
        if (this.isWritable(stream)) {
            return stream
        }

        const writable = createDeferred<void>()

        const handleDrain = () => {
            writable.resolve()
        }

        stream.once('drain', handleDrain)

        return withTimeout(writable, this.drainTimeout, () => new StreamError('Drain timeout').withStream(stream)).then(() => stream).finally(() => {
            stream.removeListener('drain', handleDrain)
        })
    }

    protected override registerStreamListeners(promise: DeferredPromise<void>, stream: TStream, isExplicitlyClosed: () => boolean, onStreamChange?: OnStreamChangeFn<TStream>) {
        let finishHandler: () => void

        stream.on('finish', finishHandler = () => {
            if (!promise.isSettled) {
                promise.reject(new StreamError('Stream finished unexpectedly').withStream(stream))
            }
        })

        return super.registerStreamListeners(promise, stream, isExplicitlyClosed, onStreamChange, () => {
            stream.removeListener('finish', finishHandler)
        })
    }

    protected override async closeStream(stream: TStream) {
        return this.end(stream).then(() => super.closeStream(stream))
    }

    protected async end(stream: TStream) {
        if (!this.isWritable(stream) || stream.writableFinished) {
            return
        }

        const promise = createDeferred<void>()
        const finishHandler = () => promise.resolve()

        stream.once('finish', finishHandler)
        stream.end()

        await withTimeout(promise, this.endTimeout, () => new StreamError('End timeout').withStream(stream)).finally(() => {
            stream.removeListener('finish', finishHandler)
        })
    }

    protected isWritable(stream: TStream) {
        return !stream.writableEnded && stream.writable
    }
}
