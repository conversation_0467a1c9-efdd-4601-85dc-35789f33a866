import type { ChannelOptions, Client } from '@grpc/grpc-js'
import { type Constructor, notNullish } from '@kdt310722/utils/common'
import { isString } from '@kdt310722/utils/string'
import { createCredential } from './credentials'
import { type MetadataObject, createMetadataInterceptor } from './metadatas'
import { type ParsedUrl, parseUrl } from './urls'

export const IGNORABLE_GRPC_ERROR_DETAILS = ['Cancelled on client']

export interface CreateGrpcClientOptions extends ChannelOptions {
    token?: string
    tokenMetadataKey?: string
    metadata?: MetadataObject
}

export function createGrpcClient<TClient extends Client>(client: Constructor<TClient>, url: ParsedUrl | string, { token, tokenMetadataKey, metadata, ...options }: CreateGrpcClientOptions = {}): TClient & { address: string } {
    const { host, port, isInsecure } = isString(url) ? parseUrl(url) : url
    const interceptor = createMetadataInterceptor({ ...(notNullish(token) && notNullish(tokenMetadataKey) ? { [tokenMetadataKey]: token } : {}), ...metadata })
    const address = `${host}:${port}`

    return Object.assign(new client(address, createCredential(isInsecure), { ...options, interceptors: [interceptor] }), { address })
}

export function isIgnorableGrpcError(error: unknown) {
    return error instanceof Error && 'details' in error && isString(error.details) && IGNORABLE_GRPC_ERROR_DETAILS.some((detail) => (error.details as string).includes(detail))
}
