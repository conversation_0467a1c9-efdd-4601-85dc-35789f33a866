import { notNullish } from '@kdt310722/utils/common'

export interface BaseErrorOptions extends ErrorOptions {
    name?: string
    code?: number | string
}

export abstract class BaseError extends Error {
    public readonly timestamp: Date
    public readonly code?: number | string

    public constructor(message?: string, { name, code, ...options }: BaseErrorOptions = {}) {
        super(message, options)

        this.name = name ?? this.constructor.name
        this.timestamp = new Date()
        this.code = code

        Object.setPrototypeOf(this, new.target.prototype)

        if (notNullish(Error.captureStackTrace)) {
            Error.captureStackTrace(this, this.constructor)
        }
    }

    public withValue<T>(key: string, value?: T): this {
        if (value !== undefined) {
            Object.defineProperty(this, key, { value, writable: false, enumerable: true, configurable: false })
        }

        return this
    }

    public toJSON() {
        const result: Record<string, unknown> = {}

        for (const key of Object.keys(this)) {
            result[key] = this[key as keyof this]
        }

        result.code = this.code
        result.timestamp = this.timestamp.toISOString()
        result.name = this.name
        result.message = this.message
        result.stack = this.stack

        if (this.cause) {
            result.cause = this.cause instanceof Error ? (this.cause as Error & { toJSON?(): unknown }).toJSON?.() ?? this.cause : this.cause
        }

        return result
    }

    public override toString() {
        return `${notNullish(this.code) ? `[${this.code}] ` : ''}${this.name}: ${this.message}`
    }
}
