import type { ClientDuplexStream, ClientReadableStream } from '@grpc/grpc-js'
import { BaseError } from './base-error'

export class StreamError extends BaseError {
    public declare stream?: ClientReadableStream<unknown> | ClientDuplexStream<unknown, unknown>

    public withStream(stream: ClientReadableStream<unknown> | ClientDuplexStream<unknown, unknown>) {
        return this.withValue('stream', stream)
    }
}
